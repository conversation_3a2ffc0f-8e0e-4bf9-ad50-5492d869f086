{"name": "crewcraft-ai-platform", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.38.5", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "@types/uuid": "^9.0.7", "@xyflow/react": "^12.8.2", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "lucide-react": "^0.303.0", "next": "14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.10.4", "recharts": "^2.8.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.56.0", "eslint-config-next": "14.0.4"}}