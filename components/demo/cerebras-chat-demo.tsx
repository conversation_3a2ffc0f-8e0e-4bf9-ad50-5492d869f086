'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useCerebrasChat } from '@/lib/hooks/use-cerebras-chat'
import { 
  BoltIcon, 
  PaperAirplaneIcon, 
  TrashIcon,
  ClockIcon,
  CpuChipIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

export function CebrasChatDemo() {
  const [inputValue, setInputValue] = useState('')
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [endTime, setEndTime] = useState<Date | null>(null)

  const {
    messages,
    isLoading,
    isStreaming,
    currentResponse,
    sendMessage,
    clearMessages,
    canSend
  } = useCerebrasChat({
    model: 'llama3.1-70b',
    temperature: 0.7,
    onComplete: () => {
      setEndTime(new Date())
    }
  })

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !canSend) return

    setStartTime(new Date())
    setEndTime(null)
    const message = inputValue
    setInputValue('')
    await sendMessage(message, 'user', true)
  }

  const getResponseTime = () => {
    if (startTime && endTime) {
      return endTime.getTime() - startTime.getTime()
    }
    return null
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <BoltIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">Cerebras Real-time Chat</h3>
            <p className="text-sm text-gray-600">Experience ultra-fast AI responses with streaming</p>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Response Time Display */}
          {getResponseTime() && (
            <div className="flex items-center gap-2 px-3 py-1 bg-green-100 rounded-full">
              <ClockIcon className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                {getResponseTime()}ms
              </span>
            </div>
          )}
          
          {/* Model Info */}
          <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 rounded-full">
            <CpuChipIcon className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Llama 3.1 70B</span>
          </div>
          
          {/* Clear Button */}
          <button
            onClick={clearMessages}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            title="Clear conversation"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="h-96 overflow-y-auto space-y-4 p-4 bg-gray-50 rounded-lg">
        {messages.length === 0 && (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <BoltIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Ready for ultra-fast AI chat</p>
              <p className="text-sm">Ask me anything to see Cerebras in action!</p>
            </div>
          </div>
        )}

        {messages.filter(msg => msg.role !== 'system').map((message, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`max-w-[80%] rounded-lg p-4 ${
              message.role === 'user'
                ? 'bg-blue-600 text-white'
                : 'bg-white border border-gray-200 text-gray-900'
            }`}>
              <p className="whitespace-pre-wrap">{message.content}</p>
              
              {message.role === 'assistant' && (
                <div className="mt-2 flex items-center gap-2 text-xs opacity-70">
                  <BoltIcon className="h-3 w-3" />
                  <span>Cerebras Ultra-Fast</span>
                  <CheckCircleIcon className="h-3 w-3 text-green-500" />
                </div>
              )}
            </div>
          </motion.div>
        ))}

        {/* Streaming Response */}
        {isStreaming && currentResponse && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="max-w-[80%] rounded-lg p-4 bg-white border border-gray-200 text-gray-900">
              <p className="whitespace-pre-wrap">{currentResponse}</p>
              <div className="mt-2 flex items-center gap-2 text-xs opacity-70">
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" />
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span>Streaming live...</span>
              </div>
            </div>
          </motion.div>
        )}

        {/* Loading State */}
        {isLoading && !isStreaming && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span className="text-sm text-gray-600">Cerebras is processing...</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Input Area */}
      <div className="flex items-end gap-3">
        <div className="flex-1">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything to test Cerebras ultra-fast responses..."
            rows={3}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>
        
        <button
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || !canSend}
          className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
        >
          <PaperAirplaneIcon className="h-5 w-5" />
          <span className="hidden sm:inline">Send</span>
        </button>
      </div>

      {/* Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {isStreaming ? '~50ms' : getResponseTime() ? `${getResponseTime()}ms` : '--'}
          </div>
          <div className="text-sm text-gray-600">Response Time</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {messages.filter(m => m.role === 'assistant').length}
          </div>
          <div className="text-sm text-gray-600">Responses</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {isStreaming ? 'Live' : 'Ready'}
          </div>
          <div className="text-sm text-gray-600">Status</div>
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <BoltIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <p className="font-medium text-blue-900 mb-1">Ultra-Fast Cerebras Integration</p>
            <p className="text-blue-800">
              This demo showcases real-time streaming responses from Cerebras' ultra-fast inference engine. 
              Experience sub-100ms response times with the Llama 3.1 70B model.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
