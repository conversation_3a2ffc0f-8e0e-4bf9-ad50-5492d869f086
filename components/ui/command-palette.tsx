'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  MagnifyingGlassIcon,
  CommandLineIcon,
  PlusIcon,
  CpuChipIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  BoltIcon,
  UserIcon,
  ClockIcon,
  FolderIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

interface Command {
  id: string
  title: string
  subtitle?: string
  icon: React.ComponentType<{ className?: string }>
  category: 'navigation' | 'actions' | 'search' | 'recent' | 'ai'
  keywords: string[]
  action: () => void
  shortcut?: string
}

interface CommandPaletteProps {
  isOpen: boolean
  onClose: () => void
}

export function CommandPalette({ isOpen, onClose }: CommandPaletteProps) {
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [recentCommands, setRecentCommands] = useState<string[]>([])
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Define all available commands
  const allCommands: Command[] = useMemo(() => [
    // Navigation
    {
      id: 'nav-dashboard',
      title: 'Go to Dashboard',
      subtitle: 'View your AI crew overview',
      icon: ChartBarIcon,
      category: 'navigation',
      keywords: ['dashboard', 'home', 'overview'],
      action: () => router.push('/'),
      shortcut: '⌘ D'
    },
    {
      id: 'nav-crews',
      title: 'Go to My Crews',
      subtitle: 'Manage your AI crews',
      icon: CpuChipIcon,
      category: 'navigation',
      keywords: ['crews', 'agents', 'manage'],
      action: () => router.push('/crews'),
      shortcut: '⌘ C'
    },
    {
      id: 'nav-templates',
      title: 'Go to Templates',
      subtitle: 'Browse crew templates',
      icon: DocumentDuplicateIcon,
      category: 'navigation',
      keywords: ['templates', 'browse', 'gallery'],
      action: () => router.push('/templates'),
      shortcut: '⌘ T'
    },
    {
      id: 'nav-analytics',
      title: 'Go to Analytics',
      subtitle: 'View performance metrics',
      icon: ChartBarIcon,
      category: 'navigation',
      keywords: ['analytics', 'metrics', 'performance'],
      action: () => router.push('/analytics'),
      shortcut: '⌘ A'
    },
    {
      id: 'nav-settings',
      title: 'Go to Settings',
      subtitle: 'Configure your account',
      icon: Cog6ToothIcon,
      category: 'navigation',
      keywords: ['settings', 'preferences', 'config'],
      action: () => router.push('/settings'),
      shortcut: '⌘ ,'
    },

    // Actions
    {
      id: 'action-new-crew',
      title: 'Create New Crew',
      subtitle: 'Start building a new AI crew',
      icon: PlusIcon,
      category: 'actions',
      keywords: ['create', 'new', 'crew', 'build'],
      action: () => router.push('/crews/new'),
      shortcut: '⌘ N'
    },
    {
      id: 'action-new-agent',
      title: 'Create New Agent',
      subtitle: 'Add a new AI agent',
      icon: BoltIcon,
      category: 'actions',
      keywords: ['create', 'new', 'agent', 'add'],
      action: () => router.push('/agents/new'),
      shortcut: '⌘ ⇧ N'
    },
    {
      id: 'action-duplicate-crew',
      title: 'Duplicate Current Crew',
      subtitle: 'Make a copy of the active crew',
      icon: DocumentDuplicateIcon,
      category: 'actions',
      keywords: ['duplicate', 'copy', 'clone'],
      action: () => console.log('Duplicate crew'),
      shortcut: '⌘ ⇧ D'
    },

    // AI Actions
    {
      id: 'ai-optimize',
      title: 'AI Optimize Crew',
      subtitle: 'Let AI suggest optimizations',
      icon: BoltIcon,
      category: 'ai',
      keywords: ['ai', 'optimize', 'improve', 'suggestions'],
      action: () => console.log('AI optimize'),
    },
    {
      id: 'ai-generate-template',
      title: 'Generate Template with AI',
      subtitle: 'Create a template using AI',
      icon: DocumentDuplicateIcon,
      category: 'ai',
      keywords: ['ai', 'generate', 'template', 'create'],
      action: () => console.log('AI generate template'),
    },
    {
      id: 'ai-debug',
      title: 'AI Debug Assistant',
      subtitle: 'Get help debugging issues',
      icon: CommandLineIcon,
      category: 'ai',
      keywords: ['ai', 'debug', 'help', 'troubleshoot'],
      action: () => console.log('AI debug'),
    },

    // Search
    {
      id: 'search-crews',
      title: 'Search Crews',
      subtitle: 'Find specific crews',
      icon: MagnifyingGlassIcon,
      category: 'search',
      keywords: ['search', 'find', 'crews'],
      action: () => console.log('Search crews'),
    },
    {
      id: 'search-templates',
      title: 'Search Templates',
      subtitle: 'Find template by name or category',
      icon: MagnifyingGlassIcon,
      category: 'search',
      keywords: ['search', 'find', 'templates'],
      action: () => console.log('Search templates'),
    },
  ], [router])

  // Filter commands based on query
  const filteredCommands = useMemo(() => {
    if (!query.trim()) {
      // Show recent commands first, then popular actions
      const recent = allCommands.filter(cmd => recentCommands.includes(cmd.id))
      const popular = allCommands.filter(cmd => 
        ['action-new-crew', 'nav-dashboard', 'nav-crews', 'ai-optimize'].includes(cmd.id) &&
        !recentCommands.includes(cmd.id)
      )
      return [...recent, ...popular].slice(0, 8)
    }

    const queryLower = query.toLowerCase()
    return allCommands
      .filter(cmd => 
        cmd.title.toLowerCase().includes(queryLower) ||
        cmd.subtitle?.toLowerCase().includes(queryLower) ||
        cmd.keywords.some(keyword => keyword.toLowerCase().includes(queryLower))
      )
      .sort((a, b) => {
        // Prioritize exact title matches
        const aExact = a.title.toLowerCase().startsWith(queryLower) ? 1 : 0
        const bExact = b.title.toLowerCase().startsWith(queryLower) ? 1 : 0
        if (aExact !== bExact) return bExact - aExact

        // Then prioritize recent commands
        const aRecent = recentCommands.includes(a.id) ? 1 : 0
        const bRecent = recentCommands.includes(b.id) ? 1 : 0
        return bRecent - aRecent
      })
      .slice(0, 10)
  }, [query, allCommands, recentCommands])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          )
          break
        case 'Enter':
          e.preventDefault()
          if (filteredCommands[selectedIndex]) {
            executeCommand(filteredCommands[selectedIndex])
          }
          break
        case 'Escape':
          e.preventDefault()
          onClose()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, filteredCommands, selectedIndex, onClose])

  // Reset selection when commands change
  useEffect(() => {
    setSelectedIndex(0)
  }, [filteredCommands])

  // Focus input when opened
  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus()
      setQuery('')
    }
  }, [isOpen])

  const executeCommand = (command: Command) => {
    // Add to recent commands
    setRecentCommands(prev => {
      const filtered = prev.filter(id => id !== command.id)
      return [command.id, ...filtered].slice(0, 5)
    })

    // Execute the command
    command.action()
    onClose()
  }

  const getCategoryIcon = (category: Command['category']) => {
    switch (category) {
      case 'navigation': return ArrowRightIcon
      case 'actions': return PlusIcon
      case 'search': return MagnifyingGlassIcon
      case 'recent': return ClockIcon
      case 'ai': return BoltIcon
      default: return FolderIcon
    }
  }

  const getCategoryColor = (category: Command['category']) => {
    switch (category) {
      case 'navigation': return 'text-blue-500'
      case 'actions': return 'text-green-500'
      case 'search': return 'text-purple-500'
      case 'recent': return 'text-orange-500'
      case 'ai': return 'text-pink-500'
      default: return 'text-gray-500'
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-[10vh] z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -20 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-2xl mx-4 overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Search Input */}
          <div className="flex items-center gap-3 p-4 border-b border-gray-200 dark:border-gray-700">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Type a command or search..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="flex-1 bg-transparent border-none outline-none text-gray-900 dark:text-white placeholder-gray-500 text-lg"
            />
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono text-gray-500 dark:text-gray-400">
              ESC
            </kbd>
          </div>

          {/* Commands List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredCommands.length === 0 ? (
              <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                <CommandLineIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No commands found</p>
                <p className="text-sm mt-1">Try a different search term</p>
              </div>
            ) : (
              <div className="py-2">
                {filteredCommands.map((command, index) => {
                  const CategoryIcon = getCategoryIcon(command.category)
                  const isSelected = index === selectedIndex
                  
                  return (
                    <motion.button
                      key={command.id}
                      onClick={() => executeCommand(command)}
                      className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                        isSelected ? 'bg-primary-50 dark:bg-primary-900/20 border-r-2 border-primary-500' : ''
                      }`}
                      whileHover={{ x: 2 }}
                    >
                      <div className={`p-2 rounded-lg ${
                        isSelected 
                          ? 'bg-primary-100 dark:bg-primary-900/40' 
                          : 'bg-gray-100 dark:bg-gray-700'
                      }`}>
                        <command.icon className={`h-4 w-4 ${
                          isSelected 
                            ? 'text-primary-600 dark:text-primary-400' 
                            : 'text-gray-600 dark:text-gray-400'
                        }`} />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className={`font-medium truncate ${
                            isSelected 
                              ? 'text-primary-900 dark:text-primary-100' 
                              : 'text-gray-900 dark:text-white'
                          }`}>
                            {command.title}
                          </p>
                          <CategoryIcon className={`h-3 w-3 ${getCategoryColor(command.category)}`} />
                        </div>
                        {command.subtitle && (
                          <p className={`text-sm truncate ${
                            isSelected 
                              ? 'text-primary-600 dark:text-primary-400' 
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            {command.subtitle}
                          </p>
                        )}
                      </div>
                      
                      {command.shortcut && (
                        <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono text-gray-500 dark:text-gray-400">
                          {command.shortcut}
                        </kbd>
                      )}
                    </motion.button>
                  )
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 rounded border">↑↓</kbd>
                Navigate
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 rounded border">↵</kbd>
                Select
              </span>
            </div>
            <span>
              {filteredCommands.length} command{filteredCommands.length !== 1 ? 's' : ''}
            </span>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
