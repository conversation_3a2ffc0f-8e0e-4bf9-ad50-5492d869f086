'use client'

import { createContext, useContext, ReactNode, useState, useEffect } from 'react'
import { ThemeProvider } from '@/components/theme/theme-provider'
import { CommandPalette } from '@/components/ui/command-palette'

interface ProvidersProps {
  children: ReactNode
}

interface AppContextType {
  isCommandPaletteOpen: boolean
  setIsCommandPaletteOpen: (open: boolean) => void
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export function useAppContext() {
  const context = useContext(AppContext)
  if (context === undefined) {
    throw new Error('useAppContext must be used within a Providers component')
  }
  return context
}

export function Providers({ children }: ProvidersProps) {
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false)

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K for command palette
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsCommandPaletteOpen(true)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  const value: AppContextType = {
    isCommandPaletteOpen,
    setIsCommandPaletteOpen,
  }

  return (
    <AppContext.Provider value={value}>
      <ThemeProvider>
        {children}
        <CommandPalette
          isOpen={isCommandPaletteOpen}
          onClose={() => setIsCommandPaletteOpen(false)}
        />
      </ThemeProvider>
    </AppContext.Provider>
  )
}
