'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useWebSocket } from '@/lib/hooks/use-websocket'
import { UserIcon, ChatBubbleLeftIcon } from '@heroicons/react/24/outline'

interface Collaborator {
  id: string
  name: string
  avatar: string
  cursor: { x: number; y: number }
  color: string
  isActive: boolean
}

interface Comment {
  id: string
  userId: string
  content: string
  position: { x: number; y: number }
  timestamp: Date
  resolved: boolean
}

interface RealTimeEditorProps {
  crewId: string
  children: React.ReactNode
}

export function RealTimeEditor({ crewId, children }: RealTimeEditorProps) {
  const [collaborators, setCollaborators] = useState<Collaborator[]>([])
  const [comments, setComments] = useState<Comment[]>([])
  const [showComments, setShowComments] = useState(true)
  const [isAddingComment, setIsAddingComment] = useState(false)
  const [commentPosition, setCommentPosition] = useState({ x: 0, y: 0 })

  const { socket, isConnected } = useWebSocket(`/crews/${crewId}/collaborate`)

  useEffect(() => {
    if (!socket) return

    socket.on('collaborator-joined', (collaborator: Collaborator) => {
      setCollaborators(prev => [...prev.filter(c => c.id !== collaborator.id), collaborator])
    })

    socket.on('collaborator-left', (collaboratorId: string) => {
      setCollaborators(prev => prev.filter(c => c.id !== collaboratorId))
    })

    socket.on('cursor-moved', ({ userId, position }: { userId: string; position: { x: number; y: number } }) => {
      setCollaborators(prev => 
        prev.map(c => c.id === userId ? { ...c, cursor: position, isActive: true } : c)
      )
    })

    socket.on('comment-added', (comment: Comment) => {
      setComments(prev => [...prev, comment])
    })

    return () => {
      socket.off('collaborator-joined')
      socket.off('collaborator-left')
      socket.off('cursor-moved')
      socket.off('comment-added')
    }
  }, [socket])

  const handleMouseMove = (e: React.MouseEvent) => {
    if (socket && isConnected) {
      socket.emit('cursor-move', {
        x: e.clientX,
        y: e.clientY
      })
    }
  }

  const handleDoubleClick = (e: React.MouseEvent) => {
    if (e.detail === 2) {
      setCommentPosition({ x: e.clientX, y: e.clientY })
      setIsAddingComment(true)
    }
  }

  return (
    <div 
      className="relative w-full h-full"
      onMouseMove={handleMouseMove}
      onDoubleClick={handleDoubleClick}
    >
      {/* Connection Status */}
      <div className="absolute top-4 right-4 z-50">
        <div className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium ${
          isConnected 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>

      {/* Collaborators List */}
      <div className="absolute top-4 left-4 z-50">
        <div className="flex items-center gap-2">
          {collaborators.map((collaborator) => (
            <motion.div
              key={collaborator.id}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              className="relative"
            >
              <div 
                className="w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white text-sm font-semibold"
                style={{ backgroundColor: collaborator.color }}
              >
                {collaborator.name.charAt(0).toUpperCase()}
              </div>
              {collaborator.isActive && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
              )}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Live Cursors */}
      <AnimatePresence>
        {collaborators.map((collaborator) => (
          collaborator.isActive && (
            <motion.div
              key={`cursor-${collaborator.id}`}
              className="absolute pointer-events-none z-40"
              style={{
                left: collaborator.cursor.x,
                top: collaborator.cursor.y,
              }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path
                  d="M0 0L20 7L8 10L0 20L0 0Z"
                  fill={collaborator.color}
                />
              </svg>
              <div 
                className="absolute top-5 left-2 px-2 py-1 rounded text-white text-xs font-medium whitespace-nowrap"
                style={{ backgroundColor: collaborator.color }}
              >
                {collaborator.name}
              </div>
            </motion.div>
          )
        ))}
      </AnimatePresence>

      {/* Comments */}
      <AnimatePresence>
        {showComments && comments.map((comment) => (
          <motion.div
            key={comment.id}
            className="absolute z-30"
            style={{
              left: comment.position.x,
              top: comment.position.y,
            }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
          >
            <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-3 shadow-lg max-w-xs">
              <div className="flex items-start gap-2">
                <ChatBubbleLeftIcon className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-gray-800">{comment.content}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {comment.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Comment Input Modal */}
      {isAddingComment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
          >
            <h3 className="text-lg font-semibold mb-4">Add Comment</h3>
            <textarea
              className="w-full p-3 border border-gray-300 rounded-lg resize-none"
              rows={3}
              placeholder="Type your comment..."
              autoFocus
            />
            <div className="flex justify-end gap-3 mt-4">
              <button
                onClick={() => setIsAddingComment(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setIsAddingComment(false)}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
              >
                Add Comment
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Main Content */}
      {children}
    </div>
  )
}
