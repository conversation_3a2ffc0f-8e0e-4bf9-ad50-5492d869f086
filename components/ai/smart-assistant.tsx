'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCerebrasChat } from '@/lib/hooks/use-cerebras-chat'
import {
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  SparklesIcon,
  LightBulbIcon,
  CpuChipIcon,
  XMarkIcon,
  MicrophoneIcon,
  StopIcon,
  BoltIcon
} from '@heroicons/react/24/outline'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  suggestions?: string[]
  actions?: Array<{
    type: 'create_agent' | 'optimize_crew' | 'generate_template'
    label: string
    data: any
  }>
}

interface SmartAssistantProps {
  isOpen: boolean
  onClose: () => void
  context?: {
    currentPage: string
    selectedCrew?: string
    recentActions?: string[]
  }
}

export function SmartAssistant({ isOpen, onClose, context }: SmartAssistantProps) {
  const [inputValue, setInputValue] = useState('')
  const [isListening, setIsListening] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Use real Cerebras chat with streaming
  const {
    messages: cerebrasMessages,
    isLoading,
    isStreaming,
    currentResponse,
    sendMessage,
    clearMessages,
    canSend
  } = useCerebrasChat({
    model: 'llama3.1-70b',
    temperature: 0.7,
    onError: (error) => {
      console.error('Cerebras error:', error)
    }
  })

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Focus input when opened
  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus()
    }
  }, [isOpen])

  // Initialize with system message and welcome
  useEffect(() => {
    if (isOpen && cerebrasMessages.length === 0) {
      // Add system context
      const systemPrompt = `You are an AI assistant for CrewCraft AI Platform. You help users create, manage, and optimize AI agent crews.

Current context: ${context?.currentPage || 'dashboard'}
Recent actions: ${context?.recentActions?.join(', ') || 'none'}

You can help with:
- Creating new agents and crews
- Optimizing performance and costs
- Explaining AI agent concepts
- Troubleshooting issues
- Generating templates and workflows

Be helpful, concise, and provide actionable suggestions. Always offer specific next steps.`

      // This will be handled by the Cerebras hook
      sendMessage(systemPrompt, 'system', false)
    }
  }, [isOpen, cerebrasMessages.length, context, sendMessage])

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || !canSend) return

    setInputValue('')
    await sendMessage(content, 'user', true)
  }

  // Voice input functionality
  const startVoiceInput = () => {
    setIsListening(true)
    // Implement speech recognition here
    setTimeout(() => {
      setIsListening(false)
      setInputValue("Create a new content writing agent")
    }, 2000)
  }

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed bottom-4 right-4 w-96 h-[600px] bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-primary-100 rounded-lg">
            <SparklesIcon className="h-5 w-5 text-primary-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">AI Assistant</h3>
            <p className="text-xs text-gray-500">Always here to help</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <XMarkIcon className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {cerebrasMessages.filter(msg => msg.role !== 'system').map((message, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[80%] rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                {/* Show Cerebras branding for assistant messages */}
                {message.role === 'assistant' && (
                  <div className="mt-2 flex items-center gap-1 text-xs opacity-70">
                    <BoltIcon className="h-3 w-3" />
                    <span>Powered by Cerebras</span>
                  </div>
                )}
              </div>
            </motion.div>
          ))}

          {/* Show current streaming response */}
          {isStreaming && currentResponse && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-start"
            >
              <div className="max-w-[80%] rounded-lg p-3 bg-gray-100 text-gray-900">
                <p className="text-sm whitespace-pre-wrap">{currentResponse}</p>
                <div className="mt-2 flex items-center gap-1 text-xs opacity-70">
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" />
                  <span>Streaming...</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {(isLoading && !isStreaming) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="bg-gray-100 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-primary-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-primary-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-primary-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span className="text-xs text-gray-500">Cerebras is thinking...</span>
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputValue)}
              placeholder="Ask me anything about your AI crews..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
            />
          </div>
          
          <button
            onClick={startVoiceInput}
            className={`p-2 rounded-lg transition-colors ${
              isListening 
                ? 'bg-red-100 text-red-600' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {isListening ? (
              <StopIcon className="h-4 w-4" />
            ) : (
              <MicrophoneIcon className="h-4 w-4" />
            )}
          </button>
          
          <button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || !canSend}
            className="p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </motion.div>
  )
}
