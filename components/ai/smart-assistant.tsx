'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  SparklesIcon,
  LightBulbIcon,
  CpuChipIcon,
  XMarkIcon,
  MicrophoneIcon,
  StopIcon
} from '@heroicons/react/24/outline'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  suggestions?: string[]
  actions?: Array<{
    type: 'create_agent' | 'optimize_crew' | 'generate_template'
    label: string
    data: any
  }>
}

interface SmartAssistantProps {
  isOpen: boolean
  onClose: () => void
  context?: {
    currentPage: string
    selectedCrew?: string
    recentActions?: string[]
  }
}

export function SmartAssistant({ isOpen, onClose, context }: SmartAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Focus input when opened
  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus()
    }
  }, [isOpen])

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        type: 'assistant',
        content: `Hi! I'm your AI assistant. I can help you create agents, optimize crews, and answer questions about your AI platform. What would you like to do today?`,
        timestamp: new Date(),
        suggestions: [
          'Create a new research agent',
          'Optimize my current crew performance',
          'Generate a template for content creation',
          'Explain how agent handoffs work'
        ]
      }
      setMessages([welcomeMessage])
    }
  }, [isOpen, messages.length])

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    // Simulate AI processing
    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: generateResponse(content),
        timestamp: new Date(),
        suggestions: generateSuggestions(content),
        actions: generateActions(content)
      }
      
      setMessages(prev => [...prev, assistantMessage])
      setIsLoading(false)
    }, 1500)
  }

  const generateResponse = (input: string): string => {
    const lowerInput = input.toLowerCase()
    
    if (lowerInput.includes('create') && lowerInput.includes('agent')) {
      return `I'll help you create a new agent! Based on your request, I recommend creating a specialized agent with the following configuration:

**Agent Type**: Research Agent
**Primary Function**: Information gathering and analysis
**Model**: GPT-4 for complex reasoning
**Tools**: Web search, document analysis, data extraction

Would you like me to generate this agent for you?`
    }
    
    if (lowerInput.includes('optimize') || lowerInput.includes('performance')) {
      return `I've analyzed your crew performance and found several optimization opportunities:

1. **Reduce Response Time**: Switch simple tasks to GPT-3.5 (30% faster)
2. **Improve Accuracy**: Add validation steps between agents
3. **Cost Optimization**: Implement smart caching for repeated queries

I can implement these optimizations automatically. Shall I proceed?`
    }
    
    if (lowerInput.includes('template')) {
      return `I can generate custom templates based on your needs! Here are some popular options:

• **Content Creation Crew**: Writer + Editor + SEO Specialist
• **Research & Analysis**: Researcher + Data Analyst + Report Generator  
• **Customer Support**: Classifier + Specialist + Quality Checker

Which type of template interests you most?`
    }
    
    return `I understand you're asking about "${input}". Let me help you with that! Based on your current context (${context?.currentPage}), I can suggest several approaches to address your needs. Would you like me to provide specific recommendations?`
  }

  const generateSuggestions = (input: string): string[] => {
    const lowerInput = input.toLowerCase()
    
    if (lowerInput.includes('agent')) {
      return [
        'Show me agent configuration options',
        'What tools can I add to this agent?',
        'How do I test the agent?'
      ]
    }
    
    if (lowerInput.includes('optimize')) {
      return [
        'Apply all optimizations',
        'Show detailed performance metrics',
        'Compare before and after results'
      ]
    }
    
    return [
      'Tell me more about this',
      'Show me examples',
      'What are the next steps?'
    ]
  }

  const generateActions = (input: string) => {
    const lowerInput = input.toLowerCase()
    
    if (lowerInput.includes('create') && lowerInput.includes('agent')) {
      return [
        {
          type: 'create_agent' as const,
          label: 'Create Research Agent',
          data: { type: 'research', model: 'gpt-4' }
        }
      ]
    }
    
    if (lowerInput.includes('optimize')) {
      return [
        {
          type: 'optimize_crew' as const,
          label: 'Apply Optimizations',
          data: { optimizations: ['model_switching', 'caching', 'validation'] }
        }
      ]
    }
    
    return []
  }

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion)
  }

  const handleActionClick = (action: any) => {
    // Handle action execution
    console.log('Executing action:', action)
    
    const confirmMessage: Message = {
      id: Date.now().toString(),
      type: 'assistant',
      content: `✅ Action executed successfully! I've ${action.label.toLowerCase()} for you. You can find it in your dashboard.`,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, confirmMessage])
  }

  const startVoiceInput = () => {
    setIsListening(true)
    // Implement speech recognition here
    setTimeout(() => {
      setIsListening(false)
      setInputValue("Create a new content writing agent")
    }, 2000)
  }

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed bottom-4 right-4 w-96 h-[600px] bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-primary-100 rounded-lg">
            <SparklesIcon className="h-5 w-5 text-primary-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">AI Assistant</h3>
            <p className="text-xs text-gray-500">Always here to help</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <XMarkIcon className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[80%] rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                
                {/* Suggestions */}
                {message.suggestions && (
                  <div className="mt-3 space-y-1">
                    {message.suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="block w-full text-left text-xs p-2 bg-white bg-opacity-20 rounded border border-white border-opacity-30 hover:bg-opacity-30 transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
                
                {/* Actions */}
                {message.actions && message.actions.length > 0 && (
                  <div className="mt-3 space-y-1">
                    {message.actions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => handleActionClick(action)}
                        className="flex items-center gap-2 w-full text-left text-xs p-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition-colors"
                      >
                        <CpuChipIcon className="h-3 w-3" />
                        {action.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="bg-gray-100 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span className="text-xs text-gray-500">AI is thinking...</span>
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputValue)}
              placeholder="Ask me anything about your AI crews..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
            />
          </div>
          
          <button
            onClick={startVoiceInput}
            className={`p-2 rounded-lg transition-colors ${
              isListening 
                ? 'bg-red-100 text-red-600' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {isListening ? (
              <StopIcon className="h-4 w-4" />
            ) : (
              <MicrophoneIcon className="h-4 w-4" />
            )}
          </button>
          
          <button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || isLoading}
            className="p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </motion.div>
  )
}
