'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from 'recharts'
import { 
  ChartBarIcon, 
  CpuChipIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  LightBulbIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  BoltIcon
} from '@heroicons/react/24/outline'

interface PerformanceMetric {
  timestamp: string
  responseTime: number
  successRate: number
  tokensUsed: number
  cost: number
  activeAgents: number
}

interface Anomaly {
  id: string
  type: 'performance' | 'cost' | 'error'
  severity: 'low' | 'medium' | 'high'
  message: string
  timestamp: Date
  affectedAgents: string[]
}

interface Recommendation {
  id: string
  type: 'optimization' | 'cost-saving' | 'performance'
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  estimatedSavings?: number
}

export function IntelligentDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [anomalies, setAnomalies] = useState<Anomaly[]>([])
  const [recommendations, setRecommendations] = useState<Recommendation[]>([])
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h')
  const [isRealTime, setIsRealTime] = useState(true)

  // Simulate real-time data updates
  useEffect(() => {
    if (!isRealTime) return

    const interval = setInterval(() => {
      const newMetric: PerformanceMetric = {
        timestamp: new Date().toISOString(),
        responseTime: Math.random() * 1000 + 200,
        successRate: Math.random() * 10 + 90,
        tokensUsed: Math.floor(Math.random() * 10000 + 5000),
        cost: Math.random() * 50 + 10,
        activeAgents: Math.floor(Math.random() * 5 + 3)
      }
      
      setMetrics(prev => [...prev.slice(-50), newMetric])
    }, 2000)

    return () => clearInterval(interval)
  }, [isRealTime])

  // Mock data initialization
  useEffect(() => {
    const mockMetrics: PerformanceMetric[] = Array.from({ length: 24 }, (_, i) => ({
      timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
      responseTime: Math.random() * 800 + 200,
      successRate: Math.random() * 15 + 85,
      tokensUsed: Math.floor(Math.random() * 8000 + 2000),
      cost: Math.random() * 40 + 5,
      activeAgents: Math.floor(Math.random() * 4 + 2)
    }))

    const mockAnomalies: Anomaly[] = [
      {
        id: '1',
        type: 'performance',
        severity: 'high',
        message: 'Response time spike detected in Research Agent',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        affectedAgents: ['research-agent-1']
      },
      {
        id: '2',
        type: 'cost',
        severity: 'medium',
        message: 'Token usage 40% above baseline',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        affectedAgents: ['content-agent-2', 'analysis-agent-1']
      }
    ]

    const mockRecommendations: Recommendation[] = [
      {
        id: '1',
        type: 'optimization',
        title: 'Optimize Agent Prompt Length',
        description: 'Reduce prompt tokens by 30% without affecting quality',
        impact: 'high',
        estimatedSavings: 150
      },
      {
        id: '2',
        type: 'cost-saving',
        title: 'Switch to GPT-3.5 for Simple Tasks',
        description: 'Use cheaper model for classification and simple queries',
        impact: 'medium',
        estimatedSavings: 300
      }
    ]

    setMetrics(mockMetrics)
    setAnomalies(mockAnomalies)
    setRecommendations(mockRecommendations)
  }, [])

  const formatCurrency = (value: number) => `$${value.toFixed(2)}`
  const formatTime = (value: number) => `${value.toFixed(0)}ms`

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Intelligent Analytics</h1>
          <p className="text-gray-600 mt-1">AI-powered insights and performance monitoring</p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Real-time Toggle */}
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={isRealTime}
              onChange={(e) => setIsRealTime(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="text-sm font-medium">Real-time</span>
            {isRealTime && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
          </label>

          {/* Time Range Selector */}
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {/* Anomaly Alerts */}
      {anomalies.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4"
        >
          <div className="flex items-center gap-2 mb-3">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
            <h3 className="font-semibold text-red-800">Active Anomalies</h3>
          </div>
          <div className="space-y-2">
            {anomalies.map((anomaly) => (
              <div key={anomaly.id} className="flex items-center justify-between bg-white rounded-lg p-3">
                <div>
                  <p className="font-medium text-gray-900">{anomaly.message}</p>
                  <p className="text-sm text-gray-600">
                    {anomaly.affectedAgents.join(', ')} • {anomaly.timestamp.toLocaleTimeString()}
                  </p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  anomaly.severity === 'high' ? 'bg-red-100 text-red-800' :
                  anomaly.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {anomaly.severity}
                </span>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Avg Response Time',
            value: formatTime(metrics[metrics.length - 1]?.responseTime || 0),
            change: '+12%',
            trend: 'up',
            icon: ClockIcon,
            color: 'blue'
          },
          {
            title: 'Success Rate',
            value: `${(metrics[metrics.length - 1]?.successRate || 0).toFixed(1)}%`,
            change: '+2.1%',
            trend: 'up',
            icon: ChartBarIcon,
            color: 'green'
          },
          {
            title: 'Daily Cost',
            value: formatCurrency(metrics[metrics.length - 1]?.cost || 0),
            change: '-8%',
            trend: 'down',
            icon: CpuChipIcon,
            color: 'purple'
          },
          {
            title: 'Active Agents',
            value: metrics[metrics.length - 1]?.activeAgents || 0,
            change: '+1',
            trend: 'up',
            icon: BoltIcon,
            color: 'orange'
          }
        ].map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
              </div>
              <div className={`p-3 rounded-lg bg-${metric.color}-100`}>
                <metric.icon className={`h-6 w-6 text-${metric.color}-600`} />
              </div>
            </div>
            <div className="flex items-center mt-4">
              {metric.trend === 'up' ? (
                <TrendingUpIcon className="h-4 w-4 text-green-500" />
              ) : (
                <TrendingDownIcon className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ml-1 ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-500 ml-1">vs last period</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold mb-4">Performance Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={metrics}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
              <YAxis />
              <Tooltip 
                labelFormatter={(value) => new Date(value).toLocaleString()}
                formatter={(value: number, name: string) => [
                  name === 'responseTime' ? formatTime(value) : value,
                  name === 'responseTime' ? 'Response Time' : 'Success Rate'
                ]}
              />
              <Line type="monotone" dataKey="responseTime" stroke="#3b82f6" strokeWidth={2} />
              <Line type="monotone" dataKey="successRate" stroke="#10b981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Cost Analysis */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold mb-4">Cost Analysis</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={metrics}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
              <YAxis />
              <Tooltip 
                labelFormatter={(value) => new Date(value).toLocaleString()}
                formatter={(value: number) => [formatCurrency(value), 'Cost']}
              />
              <Area type="monotone" dataKey="cost" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.3} />
            </AreaChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* AI Recommendations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <div className="flex items-center gap-2 mb-4">
          <LightBulbIcon className="h-5 w-5 text-yellow-500" />
          <h3 className="text-lg font-semibold">AI Recommendations</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {recommendations.map((rec) => (
            <div key={rec.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-gray-900">{rec.title}</h4>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  rec.impact === 'high' ? 'bg-green-100 text-green-800' :
                  rec.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {rec.impact} impact
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
              {rec.estimatedSavings && (
                <p className="text-sm font-medium text-green-600">
                  Estimated savings: {formatCurrency(rec.estimatedSavings)}/month
                </p>
              )}
              <button className="mt-3 px-3 py-1 bg-primary-600 text-white text-sm rounded-lg hover:bg-primary-700">
                Apply Recommendation
              </button>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  )
}
