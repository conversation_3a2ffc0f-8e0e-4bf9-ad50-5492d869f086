'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  HomeIcon, 
  CpuChipIcon, 
  DocumentDuplicateIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  PlusIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

const navigation = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { name: 'My Crews', href: '/crews', icon: CpuChipIcon },
  { name: 'Templates', href: '/templates', icon: DocumentDuplicateIcon },
  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },
  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-slate-900 px-6 pb-4">
      {/* Logo */}
      <div className="flex h-20 shrink-0 items-center">
        <Link href="/" className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600">
            <BoltIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <span className="text-xl font-bold text-white">CrewCraft</span>
            <span className="block text-xs text-slate-400">AI Platform</span>
          </div>
        </Link>
      </div>

      {/* Quick Create Button */}
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Link
          href="/crews/new"
          className="group flex w-full items-center justify-center gap-2 rounded-lg bg-primary-600 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 transition-colors"
        >
          <PlusIcon className="h-5 w-5" />
          Create New Crew
        </Link>
      </motion.div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <div className="text-xs font-semibold leading-6 text-slate-400 uppercase tracking-wider">
              Navigation
            </div>
            <ul role="list" className="-mx-2 mt-4 space-y-1">
              {navigation.map((item) => {
                // Check for exact match or if current path starts with the nav item path
                const isActive = pathname === item.href ||
                  (item.href !== '/' && pathname.startsWith(item.href))

                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={clsx(
                        isActive
                          ? 'bg-slate-800 text-white'
                          : 'text-slate-400 hover:text-white hover:bg-slate-800',
                        'group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium transition-colors'
                      )}
                    >
                      <item.icon
                        className={clsx(
                          isActive ? 'text-white' : 'text-slate-400 group-hover:text-white',
                          'h-5 w-5 shrink-0'
                        )}
                        aria-hidden="true"
                      />
                      {item.name}
                      {isActive && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="ml-auto h-2 w-2 rounded-full bg-primary-500"
                          transition={{ type: "spring", duration: 0.3 }}
                        />
                      )}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </li>

          {/* Bottom Section */}
          <li className="mt-auto">
            <div className="rounded-lg bg-slate-800 p-4">
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                  <span className="text-sm font-semibold text-white">U</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-white">User</p>
                  <p className="text-xs text-slate-400">Pro Plan</p>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </div>
  )
}
