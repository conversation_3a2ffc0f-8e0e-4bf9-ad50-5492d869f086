'use client'

import { useState, useCallback } from 'react'
import <PERSON>act<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  Panel,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { motion } from 'framer-motion'
import { 
  PlusIcon, 
  PlayIcon, 
  PauseIcon, 
  StopIcon,
  CpuChipIcon,
  BoltIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

// Custom Node Types
import { AgentNode } from './nodes/agent-node'
import { ConditionalNode } from './nodes/conditional-node'
import { TriggerNode } from './nodes/trigger-node'
import { OutputNode } from './nodes/output-node'

const nodeTypes = {
  agent: AgentNode,
  conditional: ConditionalNode,
  trigger: TriggerNode,
  output: OutputNode,
}

interface WorkflowBuilderProps {
  crewId: string
  initialNodes?: Node[]
  initialEdges?: Edge[]
  onSave?: (nodes: Node[], edges: Edge[]) => void
}

export function VisualWorkflowBuilder({ 
  crewId, 
  initialNodes = [], 
  initialEdges = [],
  onSave 
}: WorkflowBuilderProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'paused' | 'completed' | 'error'>('idle')
  const [selectedNodeType, setSelectedNodeType] = useState<'agent' | 'conditional' | 'trigger' | 'output'>('agent')

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const addNode = useCallback((type: string) => {
    const newNode: Node = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: {
        label: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
        config: {},
      },
    }
    setNodes((nds) => nds.concat(newNode))
  }, [setNodes])

  const executeWorkflow = async () => {
    setIsExecuting(true)
    setExecutionStatus('running')
    
    try {
      // Simulate workflow execution
      for (const node of nodes) {
        // Update node status during execution
        setNodes((nds) =>
          nds.map((n) =>
            n.id === node.id
              ? { ...n, data: { ...n.data, status: 'executing' } }
              : n
          )
        )
        
        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mark as completed
        setNodes((nds) =>
          nds.map((n) =>
            n.id === node.id
              ? { ...n, data: { ...n.data, status: 'completed' } }
              : n
          )
        )
      }
      
      setExecutionStatus('completed')
    } catch (error) {
      setExecutionStatus('error')
    } finally {
      setIsExecuting(false)
    }
  }

  const pauseWorkflow = () => {
    setExecutionStatus('paused')
    setIsExecuting(false)
  }

  const stopWorkflow = () => {
    setExecutionStatus('idle')
    setIsExecuting(false)
    // Reset all node statuses
    setNodes((nds) =>
      nds.map((n) => ({ ...n, data: { ...n.data, status: undefined } }))
    )
  }

  const saveWorkflow = () => {
    onSave?.(nodes, edges)
  }

  return (
    <div className="h-full w-full bg-gray-50">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        className="bg-gray-50"
      >
        <Controls />
        <MiniMap />
        <Background variant="dots" gap={12} size={1} />
        
        {/* Top Panel - Execution Controls */}
        <Panel position="top-center">
          <motion.div 
            className="bg-white rounded-lg shadow-lg border border-gray-200 p-4"
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
          >
            <div className="flex items-center gap-4">
              {/* Execution Status */}
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  executionStatus === 'running' ? 'bg-green-500 animate-pulse' :
                  executionStatus === 'paused' ? 'bg-yellow-500' :
                  executionStatus === 'completed' ? 'bg-blue-500' :
                  executionStatus === 'error' ? 'bg-red-500' :
                  'bg-gray-400'
                }`} />
                <span className="text-sm font-medium capitalize">{executionStatus}</span>
              </div>

              {/* Execution Controls */}
              <div className="flex items-center gap-2">
                <button
                  onClick={executeWorkflow}
                  disabled={isExecuting}
                  className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlayIcon className="h-4 w-4" />
                  Execute
                </button>
                
                <button
                  onClick={pauseWorkflow}
                  disabled={!isExecuting}
                  className="flex items-center gap-2 px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PauseIcon className="h-4 w-4" />
                  Pause
                </button>
                
                <button
                  onClick={stopWorkflow}
                  className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  <StopIcon className="h-4 w-4" />
                  Stop
                </button>
              </div>

              {/* Save Button */}
              <button
                onClick={saveWorkflow}
                className="flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
              >
                Save Workflow
              </button>
            </div>
          </motion.div>
        </Panel>

        {/* Left Panel - Node Palette */}
        <Panel position="top-left">
          <motion.div 
            className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-64"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
          >
            <h3 className="text-lg font-semibold mb-4">Add Nodes</h3>
            
            <div className="space-y-2">
              {[
                { type: 'agent', icon: CpuChipIcon, label: 'AI Agent', color: 'bg-blue-500' },
                { type: 'conditional', icon: BoltIcon, label: 'Conditional', color: 'bg-purple-500' },
                { type: 'trigger', icon: ClockIcon, label: 'Trigger', color: 'bg-green-500' },
                { type: 'output', icon: CheckCircleIcon, label: 'Output', color: 'bg-orange-500' },
              ].map((nodeType) => (
                <button
                  key={nodeType.type}
                  onClick={() => addNode(nodeType.type)}
                  className="w-full flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors"
                >
                  <div className={`p-2 rounded-lg ${nodeType.color}`}>
                    <nodeType.icon className="h-4 w-4 text-white" />
                  </div>
                  <span className="font-medium">{nodeType.label}</span>
                  <PlusIcon className="h-4 w-4 text-gray-400 ml-auto" />
                </button>
              ))}
            </div>

            {/* Workflow Stats */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Workflow Stats</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Nodes:</span>
                  <span>{nodes.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Connections:</span>
                  <span>{edges.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className="capitalize">{executionStatus}</span>
                </div>
              </div>
            </div>
          </motion.div>
        </Panel>
      </ReactFlow>
    </div>
  )
}
