'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  PlusIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

const actions = [
  {
    name: 'New Crew',
    href: '/crews/new',
    icon: PlusIcon,
    color: 'primary',
  },
  {
    name: 'Import Data',
    href: '/import',
    icon: DocumentDuplicateIcon,
    color: 'slate',
  },
  {
    name: 'Export Report',
    href: '/export',
    icon: ChartBarIcon,
    color: 'slate',
  },
]

export function QuickActions() {
  return (
    <div className="flex items-center gap-3">
      {actions.map((action, index) => (
        <motion.div
          key={action.name}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Link
            href={action.href}
            className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${
              action.color === 'primary'
                ? 'bg-primary-600 text-white border-primary-600 hover:bg-primary-700'
                : 'bg-white text-slate-700 border-slate-200 hover:bg-slate-50'
            }`}
          >
            <action.icon className="h-4 w-4" />
            <span className="hidden sm:inline">{action.name}</span>
          </Link>
        </motion.div>
      ))}
    </div>
  )
}
