'use client'

import { motion } from 'framer-motion'
import { Sidebar } from '@/components/layout/sidebar'
import { Header } from '@/components/layout/header'
import { WorkflowCanvas } from '@/components/workflow/workflow-canvas'
import { ExecutionPanel } from '@/components/execution/execution-panel'
import { useWorkflowStore } from '@/store/workflow-store'
import { clsx } from 'clsx'
import { SparklesIcon } from '@heroicons/react/24/outline'
export default function WorkflowPage() {
  const { isExecutionPanelOpen } = useWorkflowStore()

  return (
      <div className="h-screen flex bg-slate-50">
        <div className="flex-1 flex flex-col">
          <div className="bg-white border-b border-slate-200">
            <div className="max-w-7xl mx-auto px-6 py-4">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary-100 to-primary-300 rounded-full text-primary-700 text-sm font-medium">
                <SparklesIcon className="h-4 w-4" />
                Workflow Builder
              </div>
            </div>
          </div>
        </div>
      <Sidebar />
      
      <div className="flex-1 flex flex-col">
        <Header />
        
        <div className="flex-1 flex">
          <main className="flex-1 relative">
            <WorkflowCanvas />
          </main>
          
          {isExecutionPanelOpen && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 400, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="border-l border-primary-200 hover:border-primary-300 transition-colors"
            >
              <ExecutionPanel />
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}
