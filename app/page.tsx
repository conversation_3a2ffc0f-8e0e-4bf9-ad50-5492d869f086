'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { ActiveCrews } from '@/components/dashboard/active-crews'
import { LivePreview } from '@/components/dashboard/live-preview'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import { SmartAssistant } from '@/components/ai/smart-assistant'
import { useDashboardStats } from '@/lib/hooks/use-dashboard-stats'
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline'

export default function DashboardPage() {
  const { stats, isLoading } = useDashboardStats()
  const [isAssistantOpen, setIsAssistantOpen] = useState(false)

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Dashboard</h1>
            <p className="text-slate-600 mt-2">
              Monitor and manage your AI agent crews
            </p>
          </div>
          <QuickActions />
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCards stats={stats} isLoading={isLoading} />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Active Crews - Takes 2 columns */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <ActiveCrews />
          </motion.div>

          {/* Activity Feed - Takes 1 column */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <ActivityFeed />
          </motion.div>
        </div>

        {/* Live Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <LivePreview />
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
