'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { ActiveCrews } from '@/components/dashboard/active-crews'
import { LivePreview } from '@/components/dashboard/live-preview'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import { SmartAssistant } from '@/components/ai/smart-assistant'
import { CebrasChatDemo } from '@/components/demo/cerebras-chat-demo'
import { useDashboardStats } from '@/lib/hooks/use-dashboard-stats'
import {
  ChatBubbleLeftRightIcon,
  PlusIcon,
  CpuChipIcon,
  DocumentDuplicateIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

export default function DashboardPage() {
  const { stats, isLoading } = useDashboardStats()
  const [isAssistantOpen, setIsAssistantOpen] = useState(false)

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Dashboard</h1>
            <p className="text-slate-600 mt-2">
              Monitor and manage your AI agent crews
            </p>
          </div>
          <QuickActions />
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCards stats={stats} isLoading={isLoading} />
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Active Crews - Takes 2 columns */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <ActiveCrews />
          </motion.div>

          {/* Activity Feed - Takes 1 column */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <ActivityFeed />
          </motion.div>
        </div>

        {/* Live Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <LivePreview />
        </motion.div>

        {/* Navigation Test Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl border border-slate-200 p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Quick Navigation Test</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link
              href="/crews/new"
              className="flex items-center gap-2 p-3 bg-primary-50 border border-primary-200 rounded-lg hover:bg-primary-100 transition-colors"
            >
              <PlusIcon className="h-4 w-4 text-primary-600" />
              <span className="text-sm font-medium text-primary-900">New Crew</span>
            </Link>
            <Link
              href="/agents/new"
              className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
            >
              <CpuChipIcon className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">New Agent</span>
            </Link>
            <Link
              href="/templates"
              className="flex items-center gap-2 p-3 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <DocumentDuplicateIcon className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-900">Templates</span>
            </Link>
            <Link
              href="/analytics"
              className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors"
            >
              <ChartBarIcon className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-900">Analytics</span>
            </Link>
          </div>
        </motion.div>

        {/* Cerebras Real-time Chat Demo */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <CebrasChatDemo />
        </motion.div>
      </div>

      {/* Floating AI Assistant Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.5, type: "spring" }}
        onClick={() => setIsAssistantOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-40"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <ChatBubbleLeftRightIcon className="h-6 w-6" />
      </motion.button>

      {/* Smart Assistant */}
      <SmartAssistant
        isOpen={isAssistantOpen}
        onClose={() => setIsAssistantOpen(false)}
        context={{
          currentPage: 'dashboard',
          recentActions: ['viewed dashboard', 'checked stats']
        }}
      />
    </DashboardLayout>
  )
}
