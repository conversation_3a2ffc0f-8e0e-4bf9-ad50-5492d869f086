'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { CrewForm } from '@/components/crews/crew-form'
import { AgentBuilder } from '@/components/crews/agent-builder'
import { TaskBuilder } from '@/components/crews/task-builder'
import { CrewPreview } from '@/components/crews/crew-preview'
import { useCrews } from '@/lib/hooks/use-crews'
import { Crew, Agent, Task } from '@/types/crew'
import { 
  ArrowLeftIcon,
  CheckIcon,
  CpuChipIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import toast from 'react-hot-toast'

type Step = 'basic' | 'agents' | 'tasks' | 'preview'

export default function NewCrewPage() {
  const router = useRouter()
  const { createCrew } = useCrews()
  const [currentStep, setCurrentStep] = useState<Step>('basic')
  const [isCreating, setIsCreating] = useState(false)

  // Initialize with empty form state
  const [crewData, setCrewData] = useState<Partial<Crew>>({
    name: '',
    description: '',
    model: 'Cerebras Llama3.1-70B', // Default model selection
    agents: [],
    tasks: []
  })

  // Reset form when component mounts to ensure clean state
  useEffect(() => {
    setCurrentStep('basic')
    setCrewData({
      name: '',
      description: '',
      model: 'Cerebras Llama3.1-70B',
      agents: [],
      tasks: []
    })
    setIsCreating(false)
  }, [])

  const steps = [
    { id: 'basic', name: 'Basic Info', icon: CpuChipIcon, description: 'Name, description, and model' },
    { id: 'agents', name: 'Agents', icon: UserGroupIcon, description: 'Configure AI agents' },
    { id: 'tasks', name: 'Tasks', icon: ClipboardDocumentListIcon, description: 'Define workflows' },
    { id: 'preview', name: 'Preview', icon: EyeIcon, description: 'Review and create' }
  ]

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)

  const handleNext = () => {
    const nextIndex = currentStepIndex + 1
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex].id as Step)
    }
  }

  const handlePrevious = () => {
    const prevIndex = currentStepIndex - 1
    if (prevIndex >= 0) {
      setCurrentStep(steps[prevIndex].id as Step)
    }
  }

  const handleStepClick = (stepId: string) => {
    // Allow navigation to completed steps or current step
    const stepIndex = steps.findIndex(step => step.id === stepId)
    if (stepIndex <= currentStepIndex || isStepComplete(stepId)) {
      setCurrentStep(stepId as Step)
    }
  }

  const isStepComplete = (stepId: string) => {
    switch (stepId) {
      case 'basic':
        return !!(crewData.name && crewData.description && crewData.model)
      case 'agents':
        return (crewData.agents?.length || 0) > 0
      case 'tasks':
        return (crewData.tasks?.length || 0) > 0
      case 'preview':
        return false
      default:
        return false
    }
  }

  const canProceed = () => {
    return isStepComplete(currentStep)
  }

  const handleCreateCrew = async () => {
    try {
      setIsCreating(true)
      const newCrew = await createCrew(crewData)
      toast.success('Crew created successfully!')
      router.push(`/crews/${newCrew.id}`)
    } catch (error) {
      console.error('Error creating crew:', error)
      toast.error('Failed to create crew. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  const updateCrewData = (updates: Partial<Crew>) => {
    setCrewData(prev => ({ ...prev, ...updates }))
  }

  const addAgent = (agent: Agent) => {
    setCrewData(prev => ({
      ...prev,
      agents: [...(prev.agents || []), agent]
    }))
  }

  const updateAgent = (index: number, agent: Agent) => {
    setCrewData(prev => ({
      ...prev,
      agents: prev.agents?.map((a, i) => i === index ? agent : a) || []
    }))
  }

  const removeAgent = (index: number) => {
    setCrewData(prev => ({
      ...prev,
      agents: prev.agents?.filter((_, i) => i !== index) || []
    }))
  }

  const addTask = (task: Task) => {
    setCrewData(prev => ({
      ...prev,
      tasks: [...(prev.tasks || []), task]
    }))
  }

  const updateTask = (index: number, task: Task) => {
    setCrewData(prev => ({
      ...prev,
      tasks: prev.tasks?.map((t, i) => i === index ? task : t) || []
    }))
  }

  const removeTask = (index: number) => {
    setCrewData(prev => ({
      ...prev,
      tasks: prev.tasks?.filter((_, i) => i !== index) || []
    }))
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center gap-4">
            <Link
              href="/crews"
              className="p-2 rounded-lg border border-slate-200 hover:bg-slate-50 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 text-slate-600" />
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-slate-900">Create New Crew</h1>
              <p className="text-slate-600 mt-2">
                Build your AI agent crew step by step
              </p>
            </div>
          </div>
        </motion.div>

        {/* Progress Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl border border-slate-200 p-6"
        >
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const isActive = step.id === currentStep
              const isCompleted = isStepComplete(step.id)
              const isAccessible = index <= currentStepIndex || isCompleted
              
              return (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => handleStepClick(step.id)}
                    disabled={!isAccessible}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
                      isActive
                        ? 'bg-primary-50 border-2 border-primary-200'
                        : isCompleted
                        ? 'bg-emerald-50 border-2 border-emerald-200 hover:bg-emerald-100'
                        : isAccessible
                        ? 'hover:bg-slate-50 border-2 border-transparent'
                        : 'opacity-50 cursor-not-allowed border-2 border-transparent'
                    }`}
                  >
                    <div className={`p-2 rounded-lg ${
                      isActive
                        ? 'bg-primary-600 text-white'
                        : isCompleted
                        ? 'bg-emerald-600 text-white'
                        : 'bg-slate-100 text-slate-600'
                    }`}>
                      {isCompleted ? (
                        <CheckIcon className="h-5 w-5" />
                      ) : (
                        <step.icon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="text-left">
                      <div className={`font-medium ${
                        isActive ? 'text-primary-900' : isCompleted ? 'text-emerald-900' : 'text-slate-900'
                      }`}>
                        {step.name}
                      </div>
                      <div className="text-sm text-slate-600">
                        {step.description}
                      </div>
                    </div>
                  </button>
                  
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      isCompleted ? 'bg-emerald-300' : 'bg-slate-200'
                    }`} />
                  )}
                </div>
              )
            })}
          </div>
        </motion.div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-xl border border-slate-200 p-8"
        >
          {currentStep === 'basic' && (
            <CrewForm
              crewData={crewData}
              onUpdate={updateCrewData}
            />
          )}
          
          {currentStep === 'agents' && (
            <AgentBuilder
              agents={crewData.agents || []}
              onAddAgent={addAgent}
              onUpdateAgent={updateAgent}
              onRemoveAgent={removeAgent}
            />
          )}
          
          {currentStep === 'tasks' && (
            <TaskBuilder
              tasks={crewData.tasks || []}
              agents={crewData.agents || []}
              onAddTask={addTask}
              onUpdateTask={updateTask}
              onRemoveTask={removeTask}
            />
          )}
          
          {currentStep === 'preview' && (
            <CrewPreview
              crewData={crewData}
              onEdit={(step) => setCurrentStep(step)}
            />
          )}
        </motion.div>

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center justify-between"
        >
          <button
            onClick={handlePrevious}
            disabled={currentStepIndex === 0}
            className="px-6 py-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <div className="flex items-center gap-3">
            {currentStep === 'preview' ? (
              <button
                onClick={handleCreateCrew}
                disabled={isCreating || !canProceed()}
                className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isCreating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Crew'
                )}
              </button>
            ) : (
              <button
                onClick={handleNext}
                disabled={!canProceed()}
                className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            )}
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
