import { NextRequest, NextResponse } from 'next/server'
import { cerebras, CerebrasMessage } from '@/lib/cerebras'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { messages, options = {}, stream = true } = body

    // Validate messages
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: 'Messages array is required and cannot be empty' },
        { status: 400 }
      )
    }

    // Validate message format
    for (const message of messages) {
      if (!message.role || !message.content) {
        return NextResponse.json(
          { error: 'Each message must have role and content' },
          { status: 400 }
        )
      }
      if (!['system', 'user', 'assistant'].includes(message.role)) {
        return NextResponse.json(
          { error: 'Message role must be system, user, or assistant' },
          { status: 400 }
        )
      }
    }

    if (stream) {
      // Return streaming response
      const streamingResponse = await cerebras.createStreamingCompletion(
        messages as CerebrasMessage[],
        options
      )

      return new NextResponse(streamingResponse, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      })
    } else {
      // Return non-streaming response
      const response = await cerebras.createCompletion(
        messages as CerebrasMessage[],
        options
      )

      return NextResponse.json({
        id: 'chat-' + Date.now(),
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: options.model || 'llama3.1-70b',
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response
          },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: messages.reduce((acc: number, msg: any) => acc + msg.content.length / 4, 0),
          completion_tokens: response.length / 4,
          total_tokens: (messages.reduce((acc: number, msg: any) => acc + msg.content.length / 4, 0)) + (response.length / 4)
        }
      })
    }
  } catch (error) {
    console.error('Cerebras chat API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

// Health check endpoint
export async function GET(request: NextRequest) {
  try {
    const isConfigured = cerebras.isConfigured()
    const availableModels = cerebras.getAvailableModels()

    return NextResponse.json({
      status: 'healthy',
      configured: isConfigured,
      models: availableModels,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    })
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
