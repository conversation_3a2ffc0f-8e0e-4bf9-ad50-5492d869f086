'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { DashboardLayout } from '@/components/layouts/dashboard-layout'
import { 
  ArrowLeftIcon,
  CpuChipIcon,
  UserIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import toast from 'react-hot-toast'

interface Agent {
  id: string
  role: string
  goal: string
  backstory: string
  tools: string[]
  verbose: boolean
  allow_delegation: boolean
}

export default function NewAgentPage() {
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)
  
  // Initialize with completely empty form state
  const [agentData, setAgentData] = useState<Partial<Agent>>({
    role: '',
    goal: '',
    backstory: '',
    tools: [],
    verbose: false,
    allow_delegation: false
  })

  // Reset form when component mounts to ensure clean state
  useEffect(() => {
    setAgentData({
      role: '',
      goal: '',
      backstory: '',
      tools: [],
      verbose: false,
      allow_delegation: false
    })
    setIsCreating(false)
  }, [])

  const [availableTools] = useState([
    'web_search',
    'data_analysis',
    'content_creation',
    'image_generation',
    'code_execution',
    'file_management',
    'email_sender',
    'calendar_manager',
    'database_query',
    'api_caller'
  ])

  const handleInputChange = (field: keyof Agent, value: any) => {
    setAgentData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleToolToggle = (tool: string) => {
    setAgentData(prev => ({
      ...prev,
      tools: prev.tools?.includes(tool) 
        ? prev.tools.filter(t => t !== tool)
        : [...(prev.tools || []), tool]
    }))
  }

  const isFormValid = () => {
    return !!(
      agentData.role?.trim() &&
      agentData.goal?.trim() &&
      agentData.backstory?.trim()
    )
  }

  const handleCreateAgent = async () => {
    if (!isFormValid()) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      setIsCreating(true)
      
      // Generate unique ID
      const newAgent = {
        ...agentData,
        id: `agent-${Date.now()}`,
      } as Agent

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast.success('Agent created successfully!')
      router.push('/crews')
    } catch (error) {
      console.error('Error creating agent:', error)
      toast.error('Failed to create agent. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center gap-4">
            <Link
              href="/crews"
              className="p-2 rounded-lg border border-slate-200 hover:bg-slate-50 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 text-slate-600" />
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-slate-900">Create New Agent</h1>
              <p className="text-slate-600 mt-2">
                Configure a new AI agent with specific role and capabilities
              </p>
            </div>
          </div>
        </motion.div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl border border-slate-200 p-8 space-y-8"
        >
          {/* Basic Information */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 pb-4 border-b border-slate-200">
              <div className="p-2 bg-primary-100 rounded-lg">
                <UserIcon className="h-5 w-5 text-primary-600" />
              </div>
              <h2 className="text-xl font-semibold text-slate-900">Basic Information</h2>
            </div>

            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Agent Role *
                </label>
                <input
                  type="text"
                  value={agentData.role || ''}
                  onChange={(e) => handleInputChange('role', e.target.value)}
                  placeholder="e.g., Content Writer, Data Analyst, Research Assistant"
                  className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Goal *
                </label>
                <textarea
                  value={agentData.goal || ''}
                  onChange={(e) => handleInputChange('goal', e.target.value)}
                  placeholder="Describe what this agent should accomplish..."
                  rows={3}
                  className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Backstory *
                </label>
                <textarea
                  value={agentData.backstory || ''}
                  onChange={(e) => handleInputChange('backstory', e.target.value)}
                  placeholder="Provide context about the agent's expertise and background..."
                  rows={4}
                  className="w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                />
              </div>
            </div>
          </div>

          {/* Tools */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 pb-4 border-b border-slate-200">
              <div className="p-2 bg-orange-100 rounded-lg">
                <WrenchScrewdriverIcon className="h-5 w-5 text-orange-600" />
              </div>
              <h2 className="text-xl font-semibold text-slate-900">Available Tools</h2>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availableTools.map((tool) => (
                <button
                  key={tool}
                  onClick={() => handleToolToggle(tool)}
                  className={`p-3 rounded-lg border-2 transition-all text-left ${
                    agentData.tools?.includes(tool)
                      ? 'border-primary-200 bg-primary-50 text-primary-900'
                      : 'border-slate-200 hover:border-slate-300 text-slate-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    {agentData.tools?.includes(tool) && (
                      <CheckCircleIcon className="h-4 w-4 text-primary-600" />
                    )}
                    <span className="font-medium capitalize">
                      {tool.replace('_', ' ')}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Configuration */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 pb-4 border-b border-slate-200">
              <div className="p-2 bg-green-100 rounded-lg">
                <CpuChipIcon className="h-5 w-5 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-slate-900">Configuration</h2>
            </div>

            <div className="space-y-4">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={agentData.verbose || false}
                  onChange={(e) => handleInputChange('verbose', e.target.checked)}
                  className="w-4 h-4 text-primary-600 border-slate-300 rounded focus:ring-primary-500"
                />
                <div>
                  <span className="font-medium text-slate-900">Verbose Mode</span>
                  <p className="text-sm text-slate-600">Enable detailed logging and output</p>
                </div>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={agentData.allow_delegation || false}
                  onChange={(e) => handleInputChange('allow_delegation', e.target.checked)}
                  className="w-4 h-4 text-primary-600 border-slate-300 rounded focus:ring-primary-500"
                />
                <div>
                  <span className="font-medium text-slate-900">Allow Delegation</span>
                  <p className="text-sm text-slate-600">Allow this agent to delegate tasks to other agents</p>
                </div>
              </label>
            </div>
          </div>
        </motion.div>

        {/* Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center justify-between"
        >
          <Link
            href="/crews"
            className="px-6 py-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
          >
            Cancel
          </Link>
          
          <button
            onClick={handleCreateAgent}
            disabled={isCreating || !isFormValid()}
            className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isCreating ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Creating Agent...
              </>
            ) : (
              <>
                <CpuChipIcon className="h-4 w-4" />
                Create Agent
              </>
            )}
          </button>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
