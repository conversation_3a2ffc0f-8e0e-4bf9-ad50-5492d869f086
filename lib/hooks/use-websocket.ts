import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'

interface UseWebSocketOptions {
  autoConnect?: boolean
  reconnectAttempts?: number
  reconnectDelay?: number
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Error) => void
}

interface UseWebSocketReturn {
  socket: Socket | null
  isConnected: boolean
  isConnecting: boolean
  error: Error | null
  connect: () => void
  disconnect: () => void
  emit: (event: string, data?: any) => void
  on: (event: string, callback: (...args: any[]) => void) => void
  off: (event: string, callback?: (...args: any[]) => void) => void
}

export function useWebSocket(
  url: string,
  options: UseWebSocketOptions = {}
): UseWebSocketReturn {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 1000,
    onConnect,
    onDisconnect,
    onError
  } = options

  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const socketRef = useRef<Socket | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()

  const connect = useCallback(() => {
    if (socketRef.current?.connected) return

    setIsConnecting(true)
    setError(null)

    try {
      const socket = io(url, {
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true,
        timeout: 10000,
      })

      socket.on('connect', () => {
        setIsConnected(true)
        setIsConnecting(false)
        setError(null)
        reconnectAttemptsRef.current = 0
        onConnect?.()
      })

      socket.on('disconnect', (reason) => {
        setIsConnected(false)
        setIsConnecting(false)
        onDisconnect?.()

        // Auto-reconnect logic
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect
          return
        }

        if (reconnectAttemptsRef.current < reconnectAttempts) {
          reconnectAttemptsRef.current++
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectDelay * Math.pow(2, reconnectAttemptsRef.current - 1)) // Exponential backoff
        }
      })

      socket.on('connect_error', (err) => {
        setIsConnecting(false)
        setError(new Error(`Connection failed: ${err.message}`))
        onError?.(err)

        if (reconnectAttemptsRef.current < reconnectAttempts) {
          reconnectAttemptsRef.current++
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectDelay * Math.pow(2, reconnectAttemptsRef.current - 1))
        }
      })

      socketRef.current = socket
    } catch (err) {
      setIsConnecting(false)
      const error = err instanceof Error ? err : new Error('Unknown connection error')
      setError(error)
      onError?.(error)
    }
  }, [url, reconnectAttempts, reconnectDelay, onConnect, onDisconnect, onError])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }
    
    setIsConnected(false)
    setIsConnecting(false)
    reconnectAttemptsRef.current = 0
  }, [])

  const emit = useCallback((event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data)
    } else {
      console.warn('Socket not connected. Cannot emit event:', event)
    }
  }, [])

  const on = useCallback((event: string, callback: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback)
    }
  }, [])

  const off = useCallback((event: string, callback?: (...args: any[]) => void) => {
    if (socketRef.current) {
      if (callback) {
        socketRef.current.off(event, callback)
      } else {
        socketRef.current.off(event)
      }
    }
  }, [])

  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect, connect, disconnect])

  return {
    socket: socketRef.current,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    emit,
    on,
    off
  }
}

// Real-time data streaming hook
export function useRealTimeData<T>(
  endpoint: string,
  initialData: T,
  options: UseWebSocketOptions = {}
) {
  const [data, setData] = useState<T>(initialData)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  
  const { socket, isConnected, error } = useWebSocket(endpoint, options)

  useEffect(() => {
    if (!socket) return

    socket.on('data-update', (newData: T) => {
      setData(newData)
      setLastUpdated(new Date())
    })

    socket.on('data-patch', (patch: Partial<T>) => {
      setData(prevData => ({ ...prevData, ...patch }))
      setLastUpdated(new Date())
    })

    return () => {
      socket.off('data-update')
      socket.off('data-patch')
    }
  }, [socket])

  const updateData = useCallback((newData: Partial<T>) => {
    if (socket?.connected) {
      socket.emit('update-data', newData)
    }
  }, [socket])

  return {
    data,
    lastUpdated,
    isConnected,
    error,
    updateData
  }
}

// Performance monitoring hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState({
    responseTime: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    networkLatency: 0,
    errorRate: 0,
    throughput: 0
  })

  const { socket, isConnected } = useWebSocket('/performance', {
    autoConnect: true
  })

  useEffect(() => {
    if (!socket) return

    socket.on('performance-metrics', (newMetrics: typeof metrics) => {
      setMetrics(newMetrics)
    })

    // Request initial metrics
    socket.emit('get-performance-metrics')

    return () => {
      socket.off('performance-metrics')
    }
  }, [socket])

  const trackEvent = useCallback((event: string, data?: any) => {
    if (socket?.connected) {
      socket.emit('track-event', { event, data, timestamp: Date.now() })
    }
  }, [socket])

  const trackError = useCallback((error: Error, context?: any) => {
    if (socket?.connected) {
      socket.emit('track-error', {
        message: error.message,
        stack: error.stack,
        context,
        timestamp: Date.now()
      })
    }
  }, [socket])

  return {
    metrics,
    isConnected,
    trackEvent,
    trackError
  }
}

// Real-time notifications hook
export function useNotifications() {
  const [notifications, setNotifications] = useState<Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    timestamp: Date
    read: boolean
  }>>([])

  const { socket, isConnected } = useWebSocket('/notifications', {
    autoConnect: true
  })

  useEffect(() => {
    if (!socket) return

    socket.on('notification', (notification: any) => {
      setNotifications(prev => [
        {
          ...notification,
          timestamp: new Date(notification.timestamp),
          read: false
        },
        ...prev
      ].slice(0, 50)) // Keep only last 50 notifications
    })

    return () => {
      socket.off('notification')
    }
  }, [socket])

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    )
    
    if (socket?.connected) {
      socket.emit('mark-notification-read', id)
    }
  }, [socket])

  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    )
    
    if (socket?.connected) {
      socket.emit('mark-all-notifications-read')
    }
  }, [socket])

  const clearNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id))
    
    if (socket?.connected) {
      socket.emit('clear-notification', id)
    }
  }, [socket])

  const unreadCount = notifications.filter(n => !n.read).length

  return {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    clearNotification
  }
}
