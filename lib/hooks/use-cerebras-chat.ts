import { useState, useCallback, useRef } from 'react'
import { CerebrasMessage, parseStreamingResponse } from '@/lib/cerebras'

interface UseCebrasChatOptions {
  model?: string
  temperature?: number
  maxTokens?: number
  onError?: (error: Error) => void
  onComplete?: (message: string) => void
}

interface ChatState {
  messages: CerebrasMessage[]
  isLoading: boolean
  isStreaming: boolean
  error: string | null
  currentResponse: string
}

export function useCerebrasChat(options: UseCebrasChatOptions = {}) {
  const {
    model = 'llama3.1-70b',
    temperature = 0.7,
    maxTokens = 2048,
    onError,
    onComplete
  } = options

  const [state, setState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    isStreaming: false,
    error: null,
    currentResponse: ''
  })

  const abortControllerRef = useRef<AbortController | null>(null)

  const sendMessage = useCallback(async (
    content: string,
    role: 'user' | 'system' = 'user',
    streaming: boolean = true
  ) => {
    try {
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const newMessage: CerebrasMessage = { role, content }
      const updatedMessages = [...state.messages, newMessage]

      setState(prev => ({
        ...prev,
        messages: updatedMessages,
        isLoading: true,
        isStreaming: streaming,
        error: null,
        currentResponse: ''
      }))

      abortControllerRef.current = new AbortController()

      const response = await fetch('/api/cerebras/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: updatedMessages,
          options: {
            model,
            temperature,
            max_tokens: maxTokens
          },
          stream: streaming
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      if (streaming && response.body) {
        // Handle streaming response
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let fullResponse = ''

        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.trim() === '') continue
              
              const parsed = parseStreamingResponse(line)
              if (parsed && parsed.choices[0]?.delta?.content) {
                const content = parsed.choices[0].delta.content
                fullResponse += content
                
                setState(prev => ({
                  ...prev,
                  currentResponse: fullResponse,
                  isStreaming: true
                }))
              }
            }
          }

          // Complete the streaming
          const assistantMessage: CerebrasMessage = {
            role: 'assistant',
            content: fullResponse
          }

          setState(prev => ({
            ...prev,
            messages: [...prev.messages, assistantMessage],
            isLoading: false,
            isStreaming: false,
            currentResponse: ''
          }))

          onComplete?.(fullResponse)
        } catch (streamError) {
          if (streamError instanceof Error && streamError.name === 'AbortError') {
            return // Request was cancelled
          }
          throw streamError
        }
      } else {
        // Handle non-streaming response
        const data = await response.json()
        const assistantMessage: CerebrasMessage = {
          role: 'assistant',
          content: data.choices[0]?.message?.content || ''
        }

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage],
          isLoading: false,
          isStreaming: false,
          currentResponse: ''
        }))

        onComplete?.(assistantMessage.content)
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        isStreaming: false,
        error: errorMessage,
        currentResponse: ''
      }))

      onError?.(error instanceof Error ? error : new Error(errorMessage))
    }
  }, [state.messages, model, temperature, maxTokens, onError, onComplete])

  const clearMessages = useCallback(() => {
    setState(prev => ({
      ...prev,
      messages: [],
      error: null,
      currentResponse: ''
    }))
  }, [])

  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setState(prev => ({
        ...prev,
        isLoading: false,
        isStreaming: false,
        currentResponse: ''
      }))
    }
  }, [])

  const addSystemMessage = useCallback((content: string) => {
    const systemMessage: CerebrasMessage = { role: 'system', content }
    setState(prev => ({
      ...prev,
      messages: [systemMessage, ...prev.messages]
    }))
  }, [])

  const removeMessage = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      messages: prev.messages.filter((_, i) => i !== index)
    }))
  }, [])

  return {
    // State
    messages: state.messages,
    isLoading: state.isLoading,
    isStreaming: state.isStreaming,
    error: state.error,
    currentResponse: state.currentResponse,
    
    // Actions
    sendMessage,
    clearMessages,
    cancelRequest,
    addSystemMessage,
    removeMessage,
    
    // Computed
    hasMessages: state.messages.length > 0,
    lastMessage: state.messages[state.messages.length - 1],
    canSend: !state.isLoading && !state.isStreaming
  }
}

// Utility hook for quick single responses
export function useCerebrasCompletion(options: UseCebrasChatOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const complete = useCallback(async (
    prompt: string,
    systemPrompt?: string
  ): Promise<string> => {
    setIsLoading(true)
    setError(null)

    try {
      const messages: CerebrasMessage[] = []
      
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt })
      }
      
      messages.push({ role: 'user', content: prompt })

      const response = await fetch('/api/cerebras/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages,
          options: {
            model: options.model || 'llama3.1-70b',
            temperature: options.temperature || 0.7,
            max_tokens: options.maxTokens || 2048
          },
          stream: false
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const data = await response.json()
      const result = data.choices[0]?.message?.content || ''
      
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      options.onError?.(err instanceof Error ? err : new Error(errorMessage))
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [options])

  return {
    complete,
    isLoading,
    error
  }
}
