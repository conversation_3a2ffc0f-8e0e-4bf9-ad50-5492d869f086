/**
 * Cerebras AI API Integration
 * Provides real-time streaming chat completions with ultra-fast inference
 */

interface CerebrasMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface CerebrasStreamResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    delta: {
      content?: string
      role?: string
    }
    finish_reason?: string
  }>
}

interface CerebrasCompletionOptions {
  model?: string
  temperature?: number
  max_tokens?: number
  stream?: boolean
  top_p?: number
  frequency_penalty?: number
  presence_penalty?: number
}

class CerebrasClient {
  private apiKey: string
  private baseURL: string = 'https://api.cerebras.ai/v1'

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.CEREBRAS_API_KEY || ''
    if (!this.apiKey) {
      console.warn('Cerebras API key not found. Using mock responses.')
    }
  }

  /**
   * Create a streaming chat completion
   */
  async createStreamingCompletion(
    messages: CerebrasMessage[],
    options: CerebrasCompletionOptions = {}
  ): Promise<ReadableStream<Uint8Array>> {
    if (!this.apiKey) {
      return this.createMockStream(messages)
    }

    const {
      model = 'llama3.1-70b',
      temperature = 0.7,
      max_tokens = 2048,
      top_p = 0.9,
      frequency_penalty = 0,
      presence_penalty = 0
    } = options

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages,
          temperature,
          max_tokens,
          top_p,
          frequency_penalty,
          presence_penalty,
          stream: true,
        }),
      })

      if (!response.ok) {
        throw new Error(`Cerebras API error: ${response.status} ${response.statusText}`)
      }

      return response.body!
    } catch (error) {
      console.error('Cerebras API error:', error)
      return this.createMockStream(messages)
    }
  }

  /**
   * Create a non-streaming chat completion
   */
  async createCompletion(
    messages: CerebrasMessage[],
    options: CerebrasCompletionOptions = {}
  ): Promise<string> {
    if (!this.apiKey) {
      return this.createMockResponse(messages)
    }

    const {
      model = 'llama3.1-70b',
      temperature = 0.7,
      max_tokens = 2048,
      top_p = 0.9,
      frequency_penalty = 0,
      presence_penalty = 0
    } = options

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages,
          temperature,
          max_tokens,
          top_p,
          frequency_penalty,
          presence_penalty,
          stream: false,
        }),
      })

      if (!response.ok) {
        throw new Error(`Cerebras API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data.choices[0]?.message?.content || ''
    } catch (error) {
      console.error('Cerebras API error:', error)
      return this.createMockResponse(messages)
    }
  }

  /**
   * Create a mock streaming response for development/fallback
   */
  private createMockStream(messages: CerebrasMessage[]): ReadableStream<Uint8Array> {
    const mockResponse = this.createMockResponse(messages)
    const encoder = new TextEncoder()
    let index = 0

    return new ReadableStream({
      start(controller) {
        const interval = setInterval(() => {
          if (index >= mockResponse.length) {
            // Send final chunk
            const finalChunk: CerebrasStreamResponse = {
              id: 'mock-' + Date.now(),
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: 'llama3.1-70b',
              choices: [{
                index: 0,
                delta: {},
                finish_reason: 'stop'
              }]
            }
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`))
            controller.enqueue(encoder.encode('data: [DONE]\n\n'))
            controller.close()
            clearInterval(interval)
            return
          }

          // Send character chunk
          const chunk: CerebrasStreamResponse = {
            id: 'mock-' + Date.now(),
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: 'llama3.1-70b',
            choices: [{
              index: 0,
              delta: {
                content: mockResponse[index]
              }
            }]
          }

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`))
          index++
        }, 50) // 50ms delay between characters for realistic streaming
      }
    })
  }

  /**
   * Create a mock response for development/fallback
   */
  private createMockResponse(messages: CerebrasMessage[]): string {
    const lastMessage = messages[messages.length - 1]?.content || ''
    
    // Generate contextual mock responses
    if (lastMessage.toLowerCase().includes('create') && lastMessage.toLowerCase().includes('agent')) {
      return `I'll help you create a new AI agent! Based on your request, I recommend:

**Agent Configuration:**
- **Role**: Specialized Assistant
- **Model**: Cerebras Llama3.1-70B for ultra-fast responses
- **Capabilities**: Natural language processing, task automation, and intelligent reasoning

**Key Features:**
- Lightning-fast inference (< 100ms response time)
- Advanced reasoning capabilities
- Seamless integration with your existing workflows

Would you like me to configure specific tools or adjust the agent's parameters?`
    }

    if (lastMessage.toLowerCase().includes('optimize') || lastMessage.toLowerCase().includes('performance')) {
      return `I've analyzed your crew performance and identified several optimization opportunities:

**Performance Improvements:**
1. **Model Selection**: Switch simple tasks to Llama3.1-8B (3x faster)
2. **Parallel Processing**: Enable concurrent agent execution
3. **Caching**: Implement intelligent response caching

**Expected Results:**
- 40% faster execution times
- 25% cost reduction
- 99.9% uptime reliability

These optimizations leverage Cerebras' ultra-fast inference to maximize your crew's efficiency.`
    }

    return `Thank you for your message! I'm powered by Cerebras' ultra-fast inference technology, delivering responses in milliseconds rather than seconds.

**Current Status:**
- Model: Llama3.1-70B
- Response Time: ~50ms
- Availability: 99.9%

How can I assist you with your AI crew management today?`
  }

  /**
   * Check if the API key is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey && this.apiKey !== ''
  }

  /**
   * Get available models
   */
  getAvailableModels(): Array<{ id: string; name: string; description: string }> {
    return [
      {
        id: 'llama3.1-70b',
        name: 'Llama 3.1 70B',
        description: 'Ultra-fast inference, best for complex reasoning tasks'
      },
      {
        id: 'llama3.1-8b',
        name: 'Llama 3.1 8B',
        description: 'Lightning-fast inference, great for simple to moderate tasks'
      }
    ]
  }
}

// Export singleton instance
export const cerebras = new CerebrasClient()

// Export types
export type { CerebrasMessage, CerebrasCompletionOptions, CerebrasStreamResponse }

// Utility function to parse streaming response
export function parseStreamingResponse(chunk: string): CerebrasStreamResponse | null {
  try {
    if (chunk.startsWith('data: ')) {
      const data = chunk.slice(6).trim()
      if (data === '[DONE]') return null
      return JSON.parse(data)
    }
    return null
  } catch (error) {
    console.error('Error parsing streaming response:', error)
    return null
  }
}
