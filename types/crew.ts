export type CrewStatus = 'draft' | 'ready' | 'running' | 'completed' | 'failed' | 'pending'

export interface Agent {
  id: string
  role: string
  goal: string
  backstory: string
  tools: string[]
  verbose?: boolean
  allow_delegation?: boolean
}

export interface Task {
  id: string
  description: string
  expected_output: string
  status: 'pending' | 'running' | 'completed' | 'failed'
}

export interface Crew {
  id: string
  name: string
  description: string
  status: CrewStatus
  progress: number
  model?: string
  agents: Agent[]
  tasks: Task[]
  created_at: string
  updated_at: string
}
