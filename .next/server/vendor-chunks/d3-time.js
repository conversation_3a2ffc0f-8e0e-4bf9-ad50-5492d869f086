"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-time";
exports.ids = ["vendor-chunks/d3-time"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-time/src/day.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-time/src/day.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeDay: () => (/* binding */ timeDay),\n/* harmony export */   timeDays: () => (/* binding */ timeDays),\n/* harmony export */   unixDay: () => (/* binding */ unixDay),\n/* harmony export */   unixDays: () => (/* binding */ unixDays),\n/* harmony export */   utcDay: () => (/* binding */ utcDay),\n/* harmony export */   utcDays: () => (/* binding */ utcDays)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/./node_modules/d3-time/src/duration.js\");\n\n\nconst timeDay = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>date.setHours(0, 0, 0, 0), (date, step)=>date.setDate(date.getDate() + step), (start, end)=>(end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay, (date)=>date.getDate() - 1);\nconst timeDays = timeDay.range;\nconst utcDay = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCDate(date.getUTCDate() + step);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay;\n}, (date)=>{\n    return date.getUTCDate() - 1;\n});\nconst utcDays = utcDay.range;\nconst unixDay = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCDate(date.getUTCDate() + step);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay;\n}, (date)=>{\n    return Math.floor(date / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay);\n});\nconst unixDays = unixDay.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/day.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/duration.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-time/src/duration.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   durationDay: () => (/* binding */ durationDay),\n/* harmony export */   durationHour: () => (/* binding */ durationHour),\n/* harmony export */   durationMinute: () => (/* binding */ durationMinute),\n/* harmony export */   durationMonth: () => (/* binding */ durationMonth),\n/* harmony export */   durationSecond: () => (/* binding */ durationSecond),\n/* harmony export */   durationWeek: () => (/* binding */ durationWeek),\n/* harmony export */   durationYear: () => (/* binding */ durationYear)\n/* harmony export */ });\nconst durationSecond = 1000;\nconst durationMinute = durationSecond * 60;\nconst durationHour = durationMinute * 60;\nconst durationDay = durationHour * 24;\nconst durationWeek = durationDay * 7;\nconst durationMonth = durationDay * 30;\nconst durationYear = durationDay * 365;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtdGltZS9zcmMvZHVyYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFPLE1BQU1BLGlCQUFpQixLQUFLO0FBQzVCLE1BQU1DLGlCQUFpQkQsaUJBQWlCLEdBQUc7QUFDM0MsTUFBTUUsZUFBZUQsaUJBQWlCLEdBQUc7QUFDekMsTUFBTUUsY0FBY0QsZUFBZSxHQUFHO0FBQ3RDLE1BQU1FLGVBQWVELGNBQWMsRUFBRTtBQUNyQyxNQUFNRSxnQkFBZ0JGLGNBQWMsR0FBRztBQUN2QyxNQUFNRyxlQUFlSCxjQUFjLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvZDMtdGltZS9zcmMvZHVyYXRpb24uanM/MTVkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZHVyYXRpb25TZWNvbmQgPSAxMDAwO1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uTWludXRlID0gZHVyYXRpb25TZWNvbmQgKiA2MDtcbmV4cG9ydCBjb25zdCBkdXJhdGlvbkhvdXIgPSBkdXJhdGlvbk1pbnV0ZSAqIDYwO1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uRGF5ID0gZHVyYXRpb25Ib3VyICogMjQ7XG5leHBvcnQgY29uc3QgZHVyYXRpb25XZWVrID0gZHVyYXRpb25EYXkgKiA3O1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uTW9udGggPSBkdXJhdGlvbkRheSAqIDMwO1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uWWVhciA9IGR1cmF0aW9uRGF5ICogMzY1O1xuIl0sIm5hbWVzIjpbImR1cmF0aW9uU2Vjb25kIiwiZHVyYXRpb25NaW51dGUiLCJkdXJhdGlvbkhvdXIiLCJkdXJhdGlvbkRheSIsImR1cmF0aW9uV2VlayIsImR1cmF0aW9uTW9udGgiLCJkdXJhdGlvblllYXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/duration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/hour.js":
/*!******************************************!*\
  !*** ./node_modules/d3-time/src/hour.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeHour: () => (/* binding */ timeHour),\n/* harmony export */   timeHours: () => (/* binding */ timeHours),\n/* harmony export */   utcHour: () => (/* binding */ utcHour),\n/* harmony export */   utcHours: () => (/* binding */ utcHours)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/./node_modules/d3-time/src/duration.js\");\n\n\nconst timeHour = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setTime(date - date.getMilliseconds() - date.getSeconds() * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond - date.getMinutes() * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour;\n}, (date)=>{\n    return date.getHours();\n});\nconst timeHours = timeHour.range;\nconst utcHour = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCMinutes(0, 0, 0);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour;\n}, (date)=>{\n    return date.getUTCHours();\n});\nconst utcHours = utcHour.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/hour.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/interval.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-time/src/interval.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeInterval: () => (/* binding */ timeInterval)\n/* harmony export */ });\nconst t0 = new Date, t1 = new Date;\nfunction timeInterval(floori, offseti, count, field) {\n    function interval(date) {\n        return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n    }\n    interval.floor = (date)=>{\n        return floori(date = new Date(+date)), date;\n    };\n    interval.ceil = (date)=>{\n        return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n    };\n    interval.round = (date)=>{\n        const d0 = interval(date), d1 = interval.ceil(date);\n        return date - d0 < d1 - date ? d0 : d1;\n    };\n    interval.offset = (date, step)=>{\n        return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n    };\n    interval.range = (start, stop, step)=>{\n        const range = [];\n        start = interval.ceil(start);\n        step = step == null ? 1 : Math.floor(step);\n        if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n        let previous;\n        do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n        while (previous < start && start < stop);\n        return range;\n    };\n    interval.filter = (test)=>{\n        return timeInterval((date)=>{\n            if (date >= date) while(floori(date), !test(date))date.setTime(date - 1);\n        }, (date, step)=>{\n            if (date >= date) {\n                if (step < 0) while(++step <= 0){\n                    while(offseti(date, -1), !test(date)){} // eslint-disable-line no-empty\n                }\n                else while(--step >= 0){\n                    while(offseti(date, +1), !test(date)){} // eslint-disable-line no-empty\n                }\n            }\n        });\n    };\n    if (count) {\n        interval.count = (start, end)=>{\n            t0.setTime(+start), t1.setTime(+end);\n            floori(t0), floori(t1);\n            return Math.floor(count(t0, t1));\n        };\n        interval.every = (step)=>{\n            step = Math.floor(step);\n            return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? (d)=>field(d) % step === 0 : (d)=>interval.count(0, d) % step === 0);\n        };\n    }\n    return interval;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/interval.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/millisecond.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-time/src/millisecond.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   millisecond: () => (/* binding */ millisecond),\n/* harmony export */   milliseconds: () => (/* binding */ milliseconds)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n\nconst millisecond = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)(()=>{\n// noop\n}, (date, step)=>{\n    date.setTime(+date + step);\n}, (start, end)=>{\n    return end - start;\n});\n// An optimized implementation for this simple case.\nmillisecond.every = (k)=>{\n    k = Math.floor(k);\n    if (!isFinite(k) || !(k > 0)) return null;\n    if (!(k > 1)) return millisecond;\n    return (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setTime(Math.floor(date / k) * k);\n    }, (date, step)=>{\n        date.setTime(+date + step * k);\n    }, (start, end)=>{\n        return (end - start) / k;\n    });\n};\nconst milliseconds = millisecond.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/millisecond.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/minute.js":
/*!********************************************!*\
  !*** ./node_modules/d3-time/src/minute.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeMinute: () => (/* binding */ timeMinute),\n/* harmony export */   timeMinutes: () => (/* binding */ timeMinutes),\n/* harmony export */   utcMinute: () => (/* binding */ utcMinute),\n/* harmony export */   utcMinutes: () => (/* binding */ utcMinutes)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/./node_modules/d3-time/src/duration.js\");\n\n\nconst timeMinute = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setTime(date - date.getMilliseconds() - date.getSeconds() * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute;\n}, (date)=>{\n    return date.getMinutes();\n});\nconst timeMinutes = timeMinute.range;\nconst utcMinute = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCSeconds(0, 0);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute;\n}, (date)=>{\n    return date.getUTCMinutes();\n});\nconst utcMinutes = utcMinute.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/minute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/month.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-time/src/month.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeMonth: () => (/* binding */ timeMonth),\n/* harmony export */   timeMonths: () => (/* binding */ timeMonths),\n/* harmony export */   utcMonth: () => (/* binding */ utcMonth),\n/* harmony export */   utcMonths: () => (/* binding */ utcMonths)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n\nconst timeMonth = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setDate(1);\n    date.setHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setMonth(date.getMonth() + step);\n}, (start, end)=>{\n    return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date)=>{\n    return date.getMonth();\n});\nconst timeMonths = timeMonth.range;\nconst utcMonth = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCDate(1);\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end)=>{\n    return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date)=>{\n    return date.getUTCMonth();\n});\nconst utcMonths = utcMonth.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/month.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/second.js":
/*!********************************************!*\
  !*** ./node_modules/d3-time/src/second.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   second: () => (/* binding */ second),\n/* harmony export */   seconds: () => (/* binding */ seconds)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/./node_modules/d3-time/src/duration.js\");\n\n\nconst second = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setTime(date - date.getMilliseconds());\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond;\n}, (date)=>{\n    return date.getUTCSeconds();\n});\nconst seconds = second.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtdGltZS9zcmMvc2Vjb25kLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMkM7QUFDRTtBQUV0QyxNQUFNRSxTQUFTRiwwREFBWUEsQ0FBQyxDQUFDRztJQUNsQ0EsS0FBS0MsT0FBTyxDQUFDRCxPQUFPQSxLQUFLRSxlQUFlO0FBQzFDLEdBQUcsQ0FBQ0YsTUFBTUc7SUFDUkgsS0FBS0MsT0FBTyxDQUFDLENBQUNELE9BQU9HLE9BQU9MLHdEQUFjQTtBQUM1QyxHQUFHLENBQUNNLE9BQU9DO0lBQ1QsT0FBTyxDQUFDQSxNQUFNRCxLQUFJLElBQUtOLHdEQUFjQTtBQUN2QyxHQUFHLENBQUNFO0lBQ0YsT0FBT0EsS0FBS00sYUFBYTtBQUMzQixHQUFHO0FBRUksTUFBTUMsVUFBVVIsT0FBT1MsS0FBSyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3Jld2NyYWZ0LWFpLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL2QzLXRpbWUvc3JjL3NlY29uZC5qcz8zMzEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dGltZUludGVydmFsfSBmcm9tIFwiLi9pbnRlcnZhbC5qc1wiO1xuaW1wb3J0IHtkdXJhdGlvblNlY29uZH0gZnJvbSBcIi4vZHVyYXRpb24uanNcIjtcblxuZXhwb3J0IGNvbnN0IHNlY29uZCA9IHRpbWVJbnRlcnZhbCgoZGF0ZSkgPT4ge1xuICBkYXRlLnNldFRpbWUoZGF0ZSAtIGRhdGUuZ2V0TWlsbGlzZWNvbmRzKCkpO1xufSwgKGRhdGUsIHN0ZXApID0+IHtcbiAgZGF0ZS5zZXRUaW1lKCtkYXRlICsgc3RlcCAqIGR1cmF0aW9uU2Vjb25kKTtcbn0sIChzdGFydCwgZW5kKSA9PiB7XG4gIHJldHVybiAoZW5kIC0gc3RhcnQpIC8gZHVyYXRpb25TZWNvbmQ7XG59LCAoZGF0ZSkgPT4ge1xuICByZXR1cm4gZGF0ZS5nZXRVVENTZWNvbmRzKCk7XG59KTtcblxuZXhwb3J0IGNvbnN0IHNlY29uZHMgPSBzZWNvbmQucmFuZ2U7XG4iXSwibmFtZXMiOlsidGltZUludGVydmFsIiwiZHVyYXRpb25TZWNvbmQiLCJzZWNvbmQiLCJkYXRlIiwic2V0VGltZSIsImdldE1pbGxpc2Vjb25kcyIsInN0ZXAiLCJzdGFydCIsImVuZCIsImdldFVUQ1NlY29uZHMiLCJzZWNvbmRzIiwicmFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/second.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/ticks.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-time/src/ticks.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeTickInterval: () => (/* binding */ timeTickInterval),\n/* harmony export */   timeTicks: () => (/* binding */ timeTicks),\n/* harmony export */   utcTickInterval: () => (/* binding */ utcTickInterval),\n/* harmony export */   utcTicks: () => (/* binding */ utcTicks)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/./node_modules/d3-time/src/duration.js\");\n/* harmony import */ var _millisecond_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./millisecond.js */ \"(ssr)/./node_modules/d3-time/src/millisecond.js\");\n/* harmony import */ var _second_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./second.js */ \"(ssr)/./node_modules/d3-time/src/second.js\");\n/* harmony import */ var _minute_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./minute.js */ \"(ssr)/./node_modules/d3-time/src/minute.js\");\n/* harmony import */ var _hour_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hour.js */ \"(ssr)/./node_modules/d3-time/src/hour.js\");\n/* harmony import */ var _day_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./day.js */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var _week_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./week.js */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var _month_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./month.js */ \"(ssr)/./node_modules/d3-time/src/month.js\");\n/* harmony import */ var _year_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./year.js */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n\n\n\n\n\n\n\n\n\n\nfunction ticker(year, month, week, day, hour, minute) {\n    const tickIntervals = [\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            5,\n            5 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            15,\n            15 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            30,\n            30 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            minute,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            minute,\n            5,\n            5 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            minute,\n            15,\n            15 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            minute,\n            30,\n            30 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            hour,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            hour,\n            3,\n            3 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            hour,\n            6,\n            6 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            hour,\n            12,\n            12 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            day,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay\n        ],\n        [\n            day,\n            2,\n            2 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay\n        ],\n        [\n            week,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationWeek\n        ],\n        [\n            month,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMonth\n        ],\n        [\n            month,\n            3,\n            3 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMonth\n        ],\n        [\n            year,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationYear\n        ]\n    ];\n    function ticks(start, stop, count) {\n        const reverse = stop < start;\n        if (reverse) [start, stop] = [\n            stop,\n            start\n        ];\n        const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n        const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n        return reverse ? ticks.reverse() : ticks;\n    }\n    function tickInterval(start, stop, count) {\n        const target = Math.abs(stop - start) / count;\n        const i = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(([, , step])=>step).right(tickIntervals, target);\n        if (i === tickIntervals.length) return year.every((0,d3_array__WEBPACK_IMPORTED_MODULE_3__.tickStep)(start / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationYear, stop / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationYear, count));\n        if (i === 0) return _millisecond_js__WEBPACK_IMPORTED_MODULE_4__.millisecond.every(Math.max((0,d3_array__WEBPACK_IMPORTED_MODULE_3__.tickStep)(start, stop, count), 1));\n        const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n        return t.every(step);\n    }\n    return [\n        ticks,\n        tickInterval\n    ];\n}\nconst [utcTicks, utcTickInterval] = ticker(_year_js__WEBPACK_IMPORTED_MODULE_5__.utcYear, _month_js__WEBPACK_IMPORTED_MODULE_6__.utcMonth, _week_js__WEBPACK_IMPORTED_MODULE_7__.utcSunday, _day_js__WEBPACK_IMPORTED_MODULE_8__.unixDay, _hour_js__WEBPACK_IMPORTED_MODULE_9__.utcHour, _minute_js__WEBPACK_IMPORTED_MODULE_10__.utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(_year_js__WEBPACK_IMPORTED_MODULE_5__.timeYear, _month_js__WEBPACK_IMPORTED_MODULE_6__.timeMonth, _week_js__WEBPACK_IMPORTED_MODULE_7__.timeSunday, _day_js__WEBPACK_IMPORTED_MODULE_8__.timeDay, _hour_js__WEBPACK_IMPORTED_MODULE_9__.timeHour, _minute_js__WEBPACK_IMPORTED_MODULE_10__.timeMinute);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/ticks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/week.js":
/*!******************************************!*\
  !*** ./node_modules/d3-time/src/week.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeFriday: () => (/* binding */ timeFriday),\n/* harmony export */   timeFridays: () => (/* binding */ timeFridays),\n/* harmony export */   timeMonday: () => (/* binding */ timeMonday),\n/* harmony export */   timeMondays: () => (/* binding */ timeMondays),\n/* harmony export */   timeSaturday: () => (/* binding */ timeSaturday),\n/* harmony export */   timeSaturdays: () => (/* binding */ timeSaturdays),\n/* harmony export */   timeSunday: () => (/* binding */ timeSunday),\n/* harmony export */   timeSundays: () => (/* binding */ timeSundays),\n/* harmony export */   timeThursday: () => (/* binding */ timeThursday),\n/* harmony export */   timeThursdays: () => (/* binding */ timeThursdays),\n/* harmony export */   timeTuesday: () => (/* binding */ timeTuesday),\n/* harmony export */   timeTuesdays: () => (/* binding */ timeTuesdays),\n/* harmony export */   timeWednesday: () => (/* binding */ timeWednesday),\n/* harmony export */   timeWednesdays: () => (/* binding */ timeWednesdays),\n/* harmony export */   utcFriday: () => (/* binding */ utcFriday),\n/* harmony export */   utcFridays: () => (/* binding */ utcFridays),\n/* harmony export */   utcMonday: () => (/* binding */ utcMonday),\n/* harmony export */   utcMondays: () => (/* binding */ utcMondays),\n/* harmony export */   utcSaturday: () => (/* binding */ utcSaturday),\n/* harmony export */   utcSaturdays: () => (/* binding */ utcSaturdays),\n/* harmony export */   utcSunday: () => (/* binding */ utcSunday),\n/* harmony export */   utcSundays: () => (/* binding */ utcSundays),\n/* harmony export */   utcThursday: () => (/* binding */ utcThursday),\n/* harmony export */   utcThursdays: () => (/* binding */ utcThursdays),\n/* harmony export */   utcTuesday: () => (/* binding */ utcTuesday),\n/* harmony export */   utcTuesdays: () => (/* binding */ utcTuesdays),\n/* harmony export */   utcWednesday: () => (/* binding */ utcWednesday),\n/* harmony export */   utcWednesdays: () => (/* binding */ utcWednesdays)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/./node_modules/d3-time/src/duration.js\");\n\n\nfunction timeWeekday(i) {\n    return (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n        date.setHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setDate(date.getDate() + step * 7);\n    }, (start, end)=>{\n        return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationWeek;\n    });\n}\nconst timeSunday = timeWeekday(0);\nconst timeMonday = timeWeekday(1);\nconst timeTuesday = timeWeekday(2);\nconst timeWednesday = timeWeekday(3);\nconst timeThursday = timeWeekday(4);\nconst timeFriday = timeWeekday(5);\nconst timeSaturday = timeWeekday(6);\nconst timeSundays = timeSunday.range;\nconst timeMondays = timeMonday.range;\nconst timeTuesdays = timeTuesday.range;\nconst timeWednesdays = timeWednesday.range;\nconst timeThursdays = timeThursday.range;\nconst timeFridays = timeFriday.range;\nconst timeSaturdays = timeSaturday.range;\nfunction utcWeekday(i) {\n    return (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n        date.setUTCHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setUTCDate(date.getUTCDate() + step * 7);\n    }, (start, end)=>{\n        return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationWeek;\n    });\n}\nconst utcSunday = utcWeekday(0);\nconst utcMonday = utcWeekday(1);\nconst utcTuesday = utcWeekday(2);\nconst utcWednesday = utcWeekday(3);\nconst utcThursday = utcWeekday(4);\nconst utcFriday = utcWeekday(5);\nconst utcSaturday = utcWeekday(6);\nconst utcSundays = utcSunday.range;\nconst utcMondays = utcMonday.range;\nconst utcTuesdays = utcTuesday.range;\nconst utcWednesdays = utcWednesday.range;\nconst utcThursdays = utcThursday.range;\nconst utcFridays = utcFriday.range;\nconst utcSaturdays = utcSaturday.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/week.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-time/src/year.js":
/*!******************************************!*\
  !*** ./node_modules/d3-time/src/year.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeYear: () => (/* binding */ timeYear),\n/* harmony export */   timeYears: () => (/* binding */ timeYears),\n/* harmony export */   utcYear: () => (/* binding */ utcYear),\n/* harmony export */   utcYears: () => (/* binding */ utcYears)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/./node_modules/d3-time/src/interval.js\");\n\nconst timeYear = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setFullYear(date.getFullYear() + step);\n}, (start, end)=>{\n    return end.getFullYear() - start.getFullYear();\n}, (date)=>{\n    return date.getFullYear();\n});\n// An optimized implementation for this simple case.\ntimeYear.every = (k)=>{\n    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n        date.setMonth(0, 1);\n        date.setHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setFullYear(date.getFullYear() + step * k);\n    });\n};\nconst timeYears = timeYear.range;\nconst utcYear = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end)=>{\n    return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date)=>{\n    return date.getUTCFullYear();\n});\n// An optimized implementation for this simple case.\nutcYear.every = (k)=>{\n    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n        date.setUTCMonth(0, 1);\n        date.setUTCHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setUTCFullYear(date.getUTCFullYear() + step * k);\n    });\n};\nconst utcYears = utcYear.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-time/src/year.js\n");

/***/ })

};
;