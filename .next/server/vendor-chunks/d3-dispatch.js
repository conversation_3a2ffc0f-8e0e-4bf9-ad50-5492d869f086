"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-dispatch";
exports.ids = ["vendor-chunks/d3-dispatch"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-dispatch/src/dispatch.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-dispatch/src/dispatch.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar noop = {\n    value: ()=>{}\n};\nfunction dispatch() {\n    for(var i = 0, n = arguments.length, _ = {}, t; i < n; ++i){\n        if (!(t = arguments[i] + \"\") || t in _ || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n        _[t] = [];\n    }\n    return new Dispatch(_);\n}\nfunction Dispatch(_) {\n    this._ = _;\n}\nfunction parseTypenames(typenames, types) {\n    return typenames.trim().split(/^|\\s+/).map(function(t) {\n        var name = \"\", i = t.indexOf(\".\");\n        if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n        if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n        return {\n            type: t,\n            name: name\n        };\n    });\n}\nDispatch.prototype = dispatch.prototype = {\n    constructor: Dispatch,\n    on: function(typename, callback) {\n        var _ = this._, T = parseTypenames(typename + \"\", _), t, i = -1, n = T.length;\n        // If no callback was specified, return the callback of the given type and name.\n        if (arguments.length < 2) {\n            while(++i < n)if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n            return;\n        }\n        // If a type was specified, set the callback for the given type and name.\n        // Otherwise, if a null callback was specified, remove callbacks of the given name.\n        if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n        while(++i < n){\n            if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n            else if (callback == null) for(t in _)_[t] = set(_[t], typename.name, null);\n        }\n        return this;\n    },\n    copy: function() {\n        var copy = {}, _ = this._;\n        for(var t in _)copy[t] = _[t].slice();\n        return new Dispatch(copy);\n    },\n    call: function(type, that) {\n        if ((n = arguments.length - 2) > 0) for(var args = new Array(n), i = 0, n, t; i < n; ++i)args[i] = arguments[i + 2];\n        if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n        for(t = this._[type], i = 0, n = t.length; i < n; ++i)t[i].value.apply(that, args);\n    },\n    apply: function(type, that, args) {\n        if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n        for(var t = this._[type], i = 0, n = t.length; i < n; ++i)t[i].value.apply(that, args);\n    }\n};\nfunction get(type, name) {\n    for(var i = 0, n = type.length, c; i < n; ++i){\n        if ((c = type[i]).name === name) {\n            return c.value;\n        }\n    }\n}\nfunction set(type, name, callback) {\n    for(var i = 0, n = type.length; i < n; ++i){\n        if (type[i].name === name) {\n            type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n            break;\n        }\n    }\n    if (callback != null) type.push({\n        name: name,\n        value: callback\n    });\n    return type;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dispatch);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-dispatch/src/dispatch.js\n");

/***/ })

};
;