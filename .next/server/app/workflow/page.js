/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/workflow/page";
exports.ids = ["app/workflow/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fworkflow%2Fpage&page=%2Fworkflow%2Fpage&appPaths=%2Fworkflow%2Fpage&pagePath=private-next-app-dir%2Fworkflow%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fworkflow%2Fpage&page=%2Fworkflow%2Fpage&appPaths=%2Fworkflow%2Fpage&pagePath=private-next-app-dir%2Fworkflow%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'workflow',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/workflow/page.tsx */ \"(rsc)/./app/workflow/page.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/workflow/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/workflow/page\",\n        pathname: \"/workflow\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fworkflow%2Fpage&page=%2Fworkflow%2Fpage&appPaths=%2Fworkflow%2Fpage&pagePath=private-next-app-dir%2Fworkflow%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fworkflow%2Fpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fworkflow%2Fpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/workflow/page.tsx */ \"(ssr)/./app/workflow/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZhcHAlMkZ3b3JrZmxvdyUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8/Mjg4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9yYWpzaGFoL0Rvd25sb2Fkcy9Qcm9qZWN0cy9BSUNyZXdEZWNrZXIvYXBwL3dvcmtmbG93L3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fworkflow%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGcmFqc2hhaCUyRkRvd25sb2FkcyUyRlByb2plY3RzJTJGQUlDcmV3RGVja2VyJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySmV0QnJhaW5zX01vbm8lMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1qZXRicmFpbnMtbW9ubyUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmpldGJyYWluc01vbm8lMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBNEc7QUFDNUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vP2MxYTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhanNoYWgvRG93bmxvYWRzL1Byb2plY3RzL0FJQ3Jld0RlY2tlci9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/workflow/page.tsx":
/*!*******************************!*\
  !*** ./app/workflow/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(ssr)/./components/layout/sidebar.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/header */ \"(ssr)/./components/layout/header.tsx\");\n/* harmony import */ var _components_workflow_workflow_canvas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/workflow/workflow-canvas */ \"(ssr)/./components/workflow/workflow-canvas.tsx\");\n/* harmony import */ var _components_execution_execution_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/execution/execution-panel */ \"(ssr)/./components/execution/execution-panel.tsx\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/workflow-store */ \"(ssr)/./store/workflow-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction WorkflowPage() {\n    const { isExecutionPanelOpen } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_5__.useWorkflowStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workflow_workflow_canvas__WEBPACK_IMPORTED_MODULE_3__.WorkflowCanvas, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            isExecutionPanelOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    width: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    width: 400,\n                                    opacity: 1\n                                },\n                                exit: {\n                                    width: 0,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"border-l border-slate-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_execution_execution_panel__WEBPACK_IMPORTED_MODULE_4__.ExecutionPanel, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/workflow/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/execution/execution-logs.tsx":
/*!*************************************************!*\
  !*** ./components/execution/execution-logs.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExecutionLogs: () => (/* binding */ ExecutionLogs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Search_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Search,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Search_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Search,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Search_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Search,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ ExecutionLogs auto */ \n\n\n\nconst mockLogs = [\n    {\n        id: \"1\",\n        timestamp: \"14:32:15\",\n        level: \"info\",\n        agent: \"System\",\n        message: \"Workflow execution started\"\n    },\n    {\n        id: \"2\",\n        timestamp: \"14:32:16\",\n        level: \"info\",\n        agent: \"Research Agent\",\n        message: \"Initializing web search tools\"\n    },\n    {\n        id: \"3\",\n        timestamp: \"14:32:18\",\n        level: \"success\",\n        agent: \"Research Agent\",\n        message: \"Successfully connected to data sources\"\n    },\n    {\n        id: \"4\",\n        timestamp: \"14:32:22\",\n        level: \"info\",\n        agent: \"Research Agent\",\n        message: \"Starting market trend analysis\"\n    },\n    {\n        id: \"5\",\n        timestamp: \"14:32:45\",\n        level: \"warning\",\n        agent: \"Content Writer\",\n        message: \"Waiting for research data - queue position: 1\"\n    },\n    {\n        id: \"6\",\n        timestamp: \"14:33:12\",\n        level: \"success\",\n        agent: \"Research Agent\",\n        message: \"Market analysis completed - 247 data points collected\"\n    },\n    {\n        id: \"7\",\n        timestamp: \"14:33:15\",\n        level: \"info\",\n        agent: \"Content Writer\",\n        message: \"Received research data, starting content generation\"\n    }\n];\nconst levelStyles = {\n    info: \"text-blue-400\",\n    warning: \"text-yellow-400\",\n    error: \"text-red-400\",\n    success: \"text-green-400\"\n};\nfunction ExecutionLogs() {\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockLogs);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const filteredLogs = logs.filter((log)=>log.message.toLowerCase().includes(filter.toLowerCase()) || log.agent.toLowerCase().includes(filter.toLowerCase()));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoScroll) {\n            const interval = setInterval(()=>{\n                const newLog = {\n                    id: Date.now().toString(),\n                    timestamp: new Date().toLocaleTimeString(),\n                    level: Math.random() > 0.7 ? \"info\" : \"success\",\n                    agent: [\n                        \"Research Agent\",\n                        \"Content Writer\",\n                        \"Quality Reviewer\"\n                    ][Math.floor(Math.random() * 3)],\n                    message: [\n                        \"Processing task queue\",\n                        \"Analyzing data patterns\",\n                        \"Generating content segment\",\n                        \"Validating output quality\",\n                        \"Optimizing performance metrics\"\n                    ][Math.floor(Math.random() * 5)]\n                };\n                setLogs((prev)=>[\n                        ...prev,\n                        newLog\n                    ]);\n            }, 3000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        autoScroll\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Search_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium\",\n                                        children: \"Execution Logs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAutoScroll(!autoScroll),\n                                        className: `px-2 py-1 text-xs rounded ${autoScroll ? \"bg-green-600\" : \"bg-slate-600\"}`,\n                                        children: \"Auto-scroll\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-slate-400 hover:text-slate-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Search_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Search_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Filter logs...\",\n                                value: filter,\n                                onChange: (e)=>setFilter(e.target.value),\n                                className: \"w-full pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-sm focus:outline-none focus:border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 font-mono text-sm space-y-1\",\n                children: filteredLogs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -10\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: index * 0.02\n                        },\n                        className: \"flex items-start space-x-3 py-1 hover:bg-slate-800 rounded px-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-500 text-xs w-16 flex-shrink-0\",\n                                children: log.timestamp\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `text-xs w-20 flex-shrink-0 uppercase font-medium ${levelStyles[log.level]}`,\n                                children: [\n                                    \"[\",\n                                    log.level,\n                                    \"]\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-accent-400 text-xs w-24 flex-shrink-0\",\n                                children: [\n                                    log.agent,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-slate-300 text-xs flex-1\",\n                                children: log.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, log.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-logs.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/execution/execution-logs.tsx\n");

/***/ }),

/***/ "(ssr)/./components/execution/execution-metrics.tsx":
/*!****************************************************!*\
  !*** ./components/execution/execution-metrics.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExecutionMetrics: () => (/* binding */ ExecutionMetrics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ ExecutionMetrics auto */ \n\n\nconst performanceData = [\n    {\n        name: \"Research Agent\",\n        tasks: 12,\n        success: 94,\n        avgTime: 45\n    },\n    {\n        name: \"Content Writer\",\n        tasks: 8,\n        success: 98,\n        avgTime: 62\n    },\n    {\n        name: \"Quality Reviewer\",\n        tasks: 6,\n        success: 100,\n        avgTime: 28\n    }\n];\nconst timelineData = [\n    {\n        time: \"14:30\",\n        tokens: 120,\n        tasks: 1\n    },\n    {\n        time: \"14:31\",\n        tokens: 340,\n        tasks: 2\n    },\n    {\n        time: \"14:32\",\n        tokens: 580,\n        tasks: 4\n    },\n    {\n        time: \"14:33\",\n        tokens: 820,\n        tasks: 6\n    },\n    {\n        time: \"14:34\",\n        tokens: 1247,\n        tasks: 8\n    }\n];\nconst resourceData = [\n    {\n        name: \"CPU\",\n        value: 65,\n        color: \"#6366f1\"\n    },\n    {\n        name: \"Memory\",\n        value: 42,\n        color: \"#8b5cf6\"\n    },\n    {\n        name: \"Network\",\n        value: 28,\n        color: \"#06b6d4\"\n    },\n    {\n        name: \"Storage\",\n        value: 15,\n        color: \"#10b981\"\n    }\n];\nfunction ExecutionMetrics() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 space-y-6 h-full overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"text-sm font-medium text-slate-400 mb-2\",\n                                children: \"Total Tokens\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-primary-400\",\n                                children: \"1,247\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-green-400\",\n                                children: \"+23% from last run\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"text-sm font-medium text-slate-400 mb-2\",\n                                children: \"Avg Response Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-secondary-400\",\n                                children: \"2.3s\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-green-400\",\n                                children: \"-15% improvement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"text-sm font-medium text-slate-400 mb-2\",\n                                children: \"Success Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-400\",\n                                children: \"97%\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-green-400\",\n                                children: \"+3% from average\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"text-sm font-medium text-slate-400 mb-2\",\n                                children: \"Cost Efficiency\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-accent-400\",\n                                children: \"$0.12\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-green-400\",\n                                children: \"-8% cost reduction\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-slate-800 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-medium mb-4\",\n                        children: \"Agent Performance\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__.ResponsiveContainer, {\n                        width: \"100%\",\n                        height: 200,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.BarChart, {\n                            data: performanceData,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.CartesianGrid, {\n                                    strokeDasharray: \"3 3\",\n                                    stroke: \"#374151\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.XAxis, {\n                                    dataKey: \"name\",\n                                    stroke: \"#9ca3af\",\n                                    fontSize: 12\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.YAxis, {\n                                    stroke: \"#9ca3af\",\n                                    fontSize: 12\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                    contentStyle: {\n                                        backgroundColor: \"#1e293b\",\n                                        border: \"1px solid #374151\",\n                                        borderRadius: \"8px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Bar, {\n                                    dataKey: \"success\",\n                                    fill: \"#6366f1\",\n                                    radius: [\n                                        4,\n                                        4,\n                                        0,\n                                        0\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-slate-800 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-medium mb-4\",\n                        children: \"Token Usage Timeline\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_1__.ResponsiveContainer, {\n                        width: \"100%\",\n                        height: 150,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.LineChart, {\n                            data: timelineData,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.CartesianGrid, {\n                                    strokeDasharray: \"3 3\",\n                                    stroke: \"#374151\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.XAxis, {\n                                    dataKey: \"time\",\n                                    stroke: \"#9ca3af\",\n                                    fontSize: 12\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.YAxis, {\n                                    stroke: \"#9ca3af\",\n                                    fontSize: 12\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                    contentStyle: {\n                                        backgroundColor: \"#1e293b\",\n                                        border: \"1px solid #374151\",\n                                        borderRadius: \"8px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Line, {\n                                    type: \"monotone\",\n                                    dataKey: \"tokens\",\n                                    stroke: \"#8b5cf6\",\n                                    strokeWidth: 2,\n                                    dot: {\n                                        fill: \"#8b5cf6\",\n                                        strokeWidth: 2,\n                                        r: 4\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-slate-800 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-medium mb-4\",\n                        children: \"Resource Usage\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: resourceData.map((resource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-slate-400\",\n                                        children: resource.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 bg-slate-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                    className: \"h-2 rounded-full\",\n                                                    style: {\n                                                        backgroundColor: resource.color\n                                                    },\n                                                    initial: {\n                                                        width: 0\n                                                    },\n                                                    animate: {\n                                                        width: `${resource.value}%`\n                                                    },\n                                                    transition: {\n                                                        duration: 1,\n                                                        delay: 0.2\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium w-8\",\n                                                children: [\n                                                    resource.value,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, resource.name, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-metrics.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/execution/execution-metrics.tsx\n");

/***/ }),

/***/ "(ssr)/./components/execution/execution-panel.tsx":
/*!**************************************************!*\
  !*** ./components/execution/execution-panel.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExecutionPanel: () => (/* binding */ ExecutionPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,Pause,Play,Square,Terminal,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/workflow-store */ \"(ssr)/./store/workflow-store.ts\");\n/* harmony import */ var _execution_logs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./execution-logs */ \"(ssr)/./components/execution/execution-logs.tsx\");\n/* harmony import */ var _execution_metrics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./execution-metrics */ \"(ssr)/./components/execution/execution-metrics.tsx\");\n/* harmony import */ var _live_monitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./live-monitor */ \"(ssr)/./components/execution/live-monitor.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ExecutionPanel auto */ \n\n\n\n\n\n\n\n\nfunction ExecutionPanel() {\n    const { toggleExecutionPanel, isExecuting, executionStatus, startExecution, pauseExecution, stopExecution } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_2__.useWorkflowStore)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monitor\");\n    const tabs = [\n        {\n            id: \"monitor\",\n            label: \"Live Monitor\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"logs\",\n            label: \"Logs\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: \"metrics\",\n            label: \"Metrics\",\n            icon: _barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-slate-900 text-slate-100 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold\",\n                                        children: \"Execution Monitor\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleExecutionPanel,\n                                className: \"p-1 text-slate-400 hover:text-slate-200 rounded\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-2 h-2 rounded-full\", executionStatus === \"running\" ? \"bg-green-500 animate-pulse\" : executionStatus === \"paused\" ? \"bg-yellow-500\" : executionStatus === \"completed\" ? \"bg-blue-500\" : executionStatus === \"error\" ? \"bg-red-500\" : \"bg-slate-500\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm capitalize\",\n                                                children: executionStatus\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-3 h-3 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"2m 34s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: !isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: startExecution,\n                                    className: \"p-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: pauseExecution,\n                                            className: \"p-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: stopExecution,\n                                            className: \"p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_Pause_Play_Square_Terminal_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors\", activeTab === tab.id ? \"border-primary-500 text-primary-400 bg-slate-800\" : \"border-transparent text-slate-400 hover:text-slate-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"h-full\",\n                    children: [\n                        activeTab === \"monitor\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_live_monitor__WEBPACK_IMPORTED_MODULE_5__.LiveMonitor, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 39\n                        }, this),\n                        activeTab === \"logs\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_execution_logs__WEBPACK_IMPORTED_MODULE_3__.ExecutionLogs, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 36\n                        }, this),\n                        activeTab === \"metrics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_execution_metrics__WEBPACK_IMPORTED_MODULE_4__.ExecutionMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 39\n                        }, this)\n                    ]\n                }, activeTab, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/execution-panel.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/execution/execution-panel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/execution/live-monitor.tsx":
/*!***********************************************!*\
  !*** ./components/execution/live-monitor.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LiveMonitor: () => (/* binding */ LiveMonitor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bot,CheckCircle,Clock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ LiveMonitor auto */ \n\n\n\nconst agents = [\n    {\n        id: \"1\",\n        name: \"Research Agent\",\n        status: \"running\",\n        currentTask: \"Analyzing market trends\",\n        progress: 75,\n        tasksCompleted: 3,\n        lastUpdate: \"2s ago\"\n    },\n    {\n        id: \"2\",\n        name: \"Content Writer\",\n        status: \"waiting\",\n        currentTask: \"Waiting for research data\",\n        progress: 0,\n        tasksCompleted: 2,\n        lastUpdate: \"1m ago\"\n    },\n    {\n        id: \"3\",\n        name: \"Quality Reviewer\",\n        status: \"idle\",\n        currentTask: \"Ready for content review\",\n        progress: 0,\n        tasksCompleted: 1,\n        lastUpdate: \"5m ago\"\n    }\n];\nconst statusConfig = {\n    running: {\n        color: \"text-green-400\",\n        bg: \"bg-green-500\",\n        icon: _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    },\n    waiting: {\n        color: \"text-yellow-400\",\n        bg: \"bg-yellow-500\",\n        icon: _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    idle: {\n        color: \"text-slate-400\",\n        bg: \"bg-slate-500\",\n        icon: _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    completed: {\n        color: \"text-blue-400\",\n        bg: \"bg-blue-500\",\n        icon: _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    error: {\n        color: \"text-red-400\",\n        bg: \"bg-red-500\",\n        icon: _barrel_optimize_names_AlertTriangle_Bot_CheckCircle_Clock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    }\n};\nfunction LiveMonitor() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 space-y-4 h-full overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-slate-800 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium\",\n                                children: \"Overall Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-slate-400\",\n                                children: \"3/5 tasks completed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-slate-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full\",\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"60%\"\n                            },\n                            transition: {\n                                duration: 1\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-xs text-slate-400 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Started 2m 34s ago\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"~1m 30s remaining\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium\",\n                        children: \"Agent Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    agents.map((agent, index)=>{\n                        const config = statusConfig[agent.status];\n                        const StatusIcon = config.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            className: \"bg-slate-800 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"p-2 rounded-lg\", config.bg.replace(\"bg-\", \"bg-opacity-20 bg-\")),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-4 h-4\", config.color)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"font-medium\",\n                                                            children: agent.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-400\",\n                                                            children: agent.currentTask\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-sm font-medium capitalize\", config.color),\n                                                    children: agent.status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-slate-400\",\n                                                    children: agent.lastUpdate\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                agent.status === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-slate-400\",\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        agent.progress,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-slate-700 rounded-full h-1.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                className: \"bg-green-500 h-1.5 rounded-full\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: `${agent.progress}%`\n                                                },\n                                                transition: {\n                                                    duration: 0.5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mt-3 pt-3 border-t border-slate-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-slate-400\",\n                                            children: [\n                                                \"Tasks completed: \",\n                                                agent.tasksCompleted\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-2 h-2 rounded-full\", config.bg, agent.status === \"running\" && \"animate-pulse\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, agent.id, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold text-green-400\",\n                                children: \"94%\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-400\",\n                                children: \"Success Rate\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold text-blue-400\",\n                                children: \"1,247\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-400\",\n                                children: \"Tokens Used\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/execution/live-monitor.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/execution/live-monitor.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/header.tsx":
/*!**************************************!*\
  !*** ./components/layout/header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/workflow-store */ \"(ssr)/./store/workflow-store.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header() {\n    const { isExecuting, isExecutionPanelOpen, toggleExecutionPanel, startExecution, stopExecution } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_1__.useWorkflowStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n        initial: {\n            y: -60\n        },\n        animate: {\n            y: 0\n        },\n        className: \"h-16 glass border-b border-white/20 flex items-center justify-between px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-slate-900\",\n                                children: \"Content Pipeline\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full\",\n                                children: \"Draft\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-white/50 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-white/50 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 max-w-md mx-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Search agents, tools, or templates...\",\n                            className: \"w-full pl-10 pr-4 py-2 bg-white/50 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: !isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            onClick: startExecution,\n                            className: \"flex items-center space-x-2 px-4 py-2 gradient-primary text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Deploy Crew\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopExecution,\n                                    className: \"flex items-center space-x-2 px-3 py-2 bg-red-500 text-white rounded-lg font-medium hover:bg-red-600 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Stop\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: \"Running\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleExecutionPanel,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-2 rounded-lg transition-colors\", isExecutionPanelOpen ? \"bg-primary-100 text-primary-700\" : \"text-slate-600 hover:text-slate-900 hover:bg-white/50\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"relative p-2 text-slate-600 hover:text-slate-900 hover:bg-white/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/workflow-store */ \"(ssr)/./store/workflow-store.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Workflows\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: \"/workflow\"\n    },\n    {\n        name: \"Templates\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        href: \"/templates\"\n    },\n    {\n        name: \"Analytics\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/analytics\"\n    },\n    {\n        name: \"Team\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: \"/team\"\n    },\n    {\n        name: \"Settings\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: \"/settings\"\n    }\n];\nconst recentWorkflows = [\n    {\n        name: \"Content Pipeline\",\n        status: \"active\",\n        agents: 3\n    },\n    {\n        name: \"Research Crew\",\n        status: \"idle\",\n        agents: 2\n    },\n    {\n        name: \"Data Analysis\",\n        status: \"running\",\n        agents: 4\n    }\n];\nfunction Sidebar() {\n    const { selectedWorkflow, setSelectedWorkflow } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_1__.useWorkflowStore)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            x: -250\n        },\n        animate: {\n            x: 0\n        },\n        className: \"w-64 bg-white border-r border-slate-200 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 gradient-primary rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-bold text-slate-900\",\n                                    children: \"CrewBuilder\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: \"AI Workflow Platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 gradient-primary text-white rounded-lg hover:opacity-90 transition-opacity\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"New Workflow\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                whileHover: {\n                                    x: 4\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer\", isActive ? \"bg-primary-50 text-primary-700 border border-primary-200\" : \"text-slate-600 hover:text-slate-900 hover:bg-slate-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-slate-500 uppercase tracking-wider mb-3\",\n                                children: \"Recent Workflows\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: recentWorkflows.map((workflow)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        whileHover: {\n                                            x: 4\n                                        },\n                                        className: \"flex items-center justify-between p-2 rounded-lg hover:bg-slate-50 cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-primary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-slate-900\",\n                                                                children: workflow.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: [\n                                                                    workflow.agents,\n                                                                    \" agents\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-2 h-2 rounded-full\", workflow.status === \"active\" ? \"bg-green-500\" : workflow.status === \"running\" ? \"bg-blue-500 animate-pulse\" : \"bg-slate-300\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, workflow.name, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-slate-900\",\n                                    children: \"John Doe\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: \"Pro Plan\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAppContext: () => (/* binding */ useAppContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAppContext,Providers auto */ \n\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAppContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (context === undefined) {\n        throw new Error(\"useAppContext must be used within a Providers component\");\n    }\n    return context;\n}\nfunction Providers({ children }) {\n    const value = {\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0RDtBQVU1RCxNQUFNRSwyQkFBYUYsb0RBQWFBLENBQTZCRztBQUV0RCxTQUFTQztJQUNkLE1BQU1DLFVBQVVKLGlEQUFVQSxDQUFDQztJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1Q7QUFFTyxTQUFTRSxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQsTUFBTUMsUUFBd0I7SUFFOUI7SUFFQSxxQkFDRSw4REFBQ1AsV0FBV1EsUUFBUTtRQUFDRCxPQUFPQTtrQkFDekJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5pbnRlcmZhY2UgQXBwQ29udGV4dFR5cGUge1xuICAvLyBBZGQgYW55IGdsb2JhbCBzdGF0ZSBvciBmdW5jdGlvbnMgaGVyZVxufVxuXG5jb25zdCBBcHBDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBcHBDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBwQ29udGV4dCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXBwQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXBwQ29udGV4dCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgUHJvdmlkZXJzIGNvbXBvbmVudCcpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIGNvbnN0IHZhbHVlOiBBcHBDb250ZXh0VHlwZSA9IHtcbiAgICAvLyBJbml0aWFsaXplIGNvbnRleHQgdmFsdWVzIGhlcmVcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEFwcENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0FwcENvbnRleHQuUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsIkFwcENvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VBcHBDb250ZXh0IiwiY29udGV4dCIsIkVycm9yIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/workflow/nodes/agent-node.tsx":
/*!**************************************************!*\
  !*** ./components/workflow/nodes/agent-node.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentNode: () => (/* binding */ AgentNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactflow */ \"(ssr)/./node_modules/@reactflow/core/dist/esm/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Settings,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Settings,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Settings,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Settings,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AgentNode auto */ \n\n\n\n\n\nconst statusColors = {\n    active: \"border-green-500 bg-green-50\",\n    idle: \"border-slate-300 bg-slate-50\",\n    error: \"border-red-500 bg-red-50\",\n    running: \"border-blue-500 bg-blue-50\"\n};\nconst statusIndicators = {\n    active: \"bg-green-500\",\n    idle: \"bg-slate-400\",\n    error: \"bg-red-500\",\n    running: \"bg-blue-500 animate-pulse\"\n};\nconst AgentNode = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(({ data, selected })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        whileHover: {\n            scale: 1.02\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"min-w-[280px] bg-white border-2 rounded-xl shadow-lg transition-all duration-200\", statusColors[data.status], selected && \"ring-2 ring-primary-500 ring-offset-2\", data.status === \"active\" && \"node-glow\", data.status === \"running\" && \"node-glow animate-pulse-glow\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_4__.Handle, {\n                type: \"target\",\n                position: reactflow__WEBPACK_IMPORTED_MODULE_4__.Position.Left,\n                className: \"w-3 h-3 border-2 border-white\",\n                style: {\n                    background: \"#6366f1\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-slate-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-primary-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-slate-900\",\n                                                children: data.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-500 capitalize\",\n                                                children: data.role\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-3 h-3 rounded-full\", statusIndicators[data.status])\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600\",\n                        children: data.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-slate-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-3 h-3 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-slate-900\",\n                                            children: data.metrics.tasksCompleted\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: \"Tasks\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-3 h-3 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-slate-900\",\n                                            children: [\n                                                data.metrics.successRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: \"Success\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs font-medium text-slate-700\",\n                                children: \"Tools\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Settings_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-3 h-3 text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: data.tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full\",\n                                children: tool\n                            }, tool, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_4__.Handle, {\n                type: \"source\",\n                position: reactflow__WEBPACK_IMPORTED_MODULE_4__.Position.Right,\n                className: \"w-3 h-3 border-2 border-white\",\n                style: {\n                    background: \"#6366f1\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/agent-node.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n});\nAgentNode.displayName = \"AgentNode\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/workflow/nodes/agent-node.tsx\n");

/***/ }),

/***/ "(ssr)/./components/workflow/nodes/task-node.tsx":
/*!*************************************************!*\
  !*** ./components/workflow/nodes/task-node.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskNode: () => (/* binding */ TaskNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactflow */ \"(ssr)/./node_modules/@reactflow/core/dist/esm/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Play!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Play!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Play!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Play!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TaskNode auto */ \n\n\n\n\n\nconst statusColors = {\n    pending: \"border-slate-300 bg-slate-50\",\n    running: \"border-blue-500 bg-blue-50\",\n    completed: \"border-green-500 bg-green-50\",\n    failed: \"border-red-500 bg-red-50\"\n};\nconst statusIcons = {\n    pending: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    running: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    completed: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    failed: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n};\nconst statusIndicators = {\n    pending: \"bg-slate-400\",\n    running: \"bg-blue-500 animate-pulse\",\n    completed: \"bg-green-500\",\n    failed: \"bg-red-500\"\n};\nconst TaskNode = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(({ data, selected })=>{\n    const StatusIcon = statusIcons[data.status];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        whileHover: {\n            scale: 1.02\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"min-w-[240px] bg-white border-2 rounded-xl shadow-lg transition-all duration-200\", statusColors[data.status], selected && \"ring-2 ring-secondary-500 ring-offset-2\", data.status === \"running\" && \"node-glow-secondary animate-pulse-glow\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_8__.Handle, {\n                type: \"target\",\n                position: reactflow__WEBPACK_IMPORTED_MODULE_8__.Position.Left,\n                className: \"w-3 h-3 border-2 border-white\",\n                style: {\n                    background: \"#8b5cf6\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-slate-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-secondary-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                            className: \"w-4 h-4 text-secondary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-slate-900\",\n                                                children: data.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-500 capitalize\",\n                                                children: data.type\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-3 h-3 rounded-full\", statusIndicators[data.status])\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600\",\n                        children: data.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            data.status === \"running\" && data.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-slate-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs font-medium text-slate-700\",\n                                children: \"Progress\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-slate-500\",\n                                children: [\n                                    data.progress,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-slate-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"bg-blue-500 h-2 rounded-full\",\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: `${data.progress}%`\n                            },\n                            transition: {\n                                duration: 0.5\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined),\n            data.assignedAgent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-medium text-slate-700\",\n                            children: \"Assigned to\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-secondary-600 font-medium\",\n                            children: data.assignedAgent\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_8__.Handle, {\n                type: \"source\",\n                position: reactflow__WEBPACK_IMPORTED_MODULE_8__.Position.Right,\n                className: \"w-3 h-3 border-2 border-white\",\n                style: {\n                    background: \"#8b5cf6\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/nodes/task-node.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n});\nTaskNode.displayName = \"TaskNode\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/workflow/nodes/task-node.tsx\n");

/***/ }),

/***/ "(ssr)/./components/workflow/toolbox-panel.tsx":
/*!***********************************************!*\
  !*** ./components/workflow/toolbox-panel.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolboxPanel: () => (/* binding */ ToolboxPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckSquare,Database,Globe,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckSquare,Database,Globe,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckSquare,Database,Globe,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckSquare,Database,Globe,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckSquare,Database,Globe,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToolboxPanel auto */ \n\n\nconst tools = [\n    {\n        type: \"agent\",\n        icon: _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        label: \"AI Agent\",\n        description: \"Intelligent agent for task execution\",\n        color: \"bg-primary-500\"\n    },\n    {\n        type: \"task\",\n        icon: _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        label: \"Task Node\",\n        description: \"Define workflow tasks and processes\",\n        color: \"bg-secondary-500\"\n    },\n    {\n        type: \"data\",\n        icon: _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        label: \"Data Source\",\n        description: \"Connect to external data sources\",\n        color: \"bg-accent-500\"\n    },\n    {\n        type: \"api\",\n        icon: _barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        label: \"API Connector\",\n        description: \"Integrate with external APIs\",\n        color: \"bg-green-500\"\n    }\n];\nfunction ToolboxPanel({ onClose }) {\n    const onDragStart = (event, nodeType)=>{\n        event.dataTransfer.setData(\"application/reactflow\", nodeType);\n        event.dataTransfer.effectAllowed = \"move\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        className: \"glass rounded-xl p-4 w-64 shadow-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-slate-900\",\n                        children: \"Toolbox\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 text-slate-400 hover:text-slate-600 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckSquare_Database_Globe_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        draggable: true,\n                        onDragStart: (event)=>onDragStart(event, tool.type),\n                        className: \"flex items-center space-x-3 p-3 bg-white rounded-lg border border-slate-200 cursor-grab active:cursor-grabbing hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-2 ${tool.color} rounded-lg`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-slate-900\",\n                                        children: tool.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-500\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, tool.type, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-slate-500 text-center\",\n                    children: \"Drag and drop to add to workflow\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/toolbox-panel.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/workflow/toolbox-panel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/workflow/workflow-canvas.tsx":
/*!*************************************************!*\
  !*** ./components/workflow/workflow-canvas.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WorkflowCanvas: () => (/* binding */ WorkflowCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactflow */ \"(ssr)/./node_modules/@reactflow/core/dist/esm/index.mjs\");\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactflow */ \"(ssr)/./node_modules/@reactflow/controls/dist/esm/index.mjs\");\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactflow */ \"(ssr)/./node_modules/@reactflow/minimap/dist/esm/index.mjs\");\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactflow */ \"(ssr)/./node_modules/@reactflow/background/dist/esm/index.mjs\");\n/* harmony import */ var reactflow_dist_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactflow/dist/style.css */ \"(ssr)/./node_modules/reactflow/dist/style.css\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _nodes_agent_node__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nodes/agent-node */ \"(ssr)/./components/workflow/nodes/agent-node.tsx\");\n/* harmony import */ var _nodes_task_node__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./nodes/task-node */ \"(ssr)/./components/workflow/nodes/task-node.tsx\");\n/* harmony import */ var _toolbox_panel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./toolbox-panel */ \"(ssr)/./components/workflow/toolbox-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ WorkflowCanvas auto */ \n\n\n\n\n\n\n\nconst nodeTypes = {\n    agent: _nodes_agent_node__WEBPACK_IMPORTED_MODULE_3__.AgentNode,\n    task: _nodes_task_node__WEBPACK_IMPORTED_MODULE_4__.TaskNode\n};\nconst initialNodes = [\n    {\n        id: \"1\",\n        type: \"agent\",\n        position: {\n            x: 250,\n            y: 100\n        },\n        data: {\n            label: \"Research Agent\",\n            role: \"researcher\",\n            status: \"active\",\n            description: \"Conducts comprehensive research and data gathering\",\n            tools: [\n                \"web_search\",\n                \"data_analysis\"\n            ],\n            metrics: {\n                tasksCompleted: 12,\n                successRate: 94\n            }\n        }\n    },\n    {\n        id: \"2\",\n        type: \"agent\",\n        position: {\n            x: 500,\n            y: 200\n        },\n        data: {\n            label: \"Content Writer\",\n            role: \"writer\",\n            status: \"idle\",\n            description: \"Creates engaging content based on research findings\",\n            tools: [\n                \"content_generation\",\n                \"seo_optimization\"\n            ],\n            metrics: {\n                tasksCompleted: 8,\n                successRate: 98\n            }\n        }\n    },\n    {\n        id: \"3\",\n        type: \"task\",\n        position: {\n            x: 750,\n            y: 150\n        },\n        data: {\n            label: \"Content Review\",\n            type: \"review\",\n            status: \"pending\",\n            description: \"Review and approve generated content\",\n            assignedAgent: \"reviewer\"\n        }\n    }\n];\nconst initialEdges = [\n    {\n        id: \"e1-2\",\n        source: \"1\",\n        target: \"2\",\n        type: \"smoothstep\",\n        animated: true,\n        style: {\n            stroke: \"#6366f1\",\n            strokeWidth: 2\n        }\n    },\n    {\n        id: \"e2-3\",\n        source: \"2\",\n        target: \"3\",\n        type: \"smoothstep\",\n        animated: true,\n        style: {\n            stroke: \"#8b5cf6\",\n            strokeWidth: 2\n        }\n    }\n];\nfunction WorkflowCanvas() {\n    const [nodes, setNodes, onNodesChange] = (0,reactflow__WEBPACK_IMPORTED_MODULE_6__.useNodesState)(initialNodes);\n    const [edges, setEdges, onEdgesChange] = (0,reactflow__WEBPACK_IMPORTED_MODULE_6__.useEdgesState)(initialEdges);\n    const [isToolboxOpen, setIsToolboxOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const reactFlowWrapper = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((params)=>setEdges((eds)=>(0,reactflow__WEBPACK_IMPORTED_MODULE_6__.addEdge)(params, eds)), [\n        setEdges\n    ]);\n    const onDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        event.preventDefault();\n        event.dataTransfer.dropEffect = \"move\";\n    }, []);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        event.preventDefault();\n        const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();\n        const type = event.dataTransfer.getData(\"application/reactflow\");\n        if (typeof type === \"undefined\" || !type || !reactFlowBounds) {\n            return;\n        }\n        const position = {\n            x: event.clientX - reactFlowBounds.left,\n            y: event.clientY - reactFlowBounds.top\n        };\n        const newNode = {\n            id: `${nodes.length + 1}`,\n            type,\n            position,\n            data: {\n                label: `${type} ${nodes.length + 1}`,\n                status: \"idle\"\n            }\n        };\n        setNodes((nds)=>nds.concat(newNode));\n    }, [\n        nodes,\n        setNodes\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full relative\",\n        ref: reactFlowWrapper,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_6__.ReactFlow, {\n                nodes: nodes,\n                edges: edges,\n                onNodesChange: onNodesChange,\n                onEdgesChange: onEdgesChange,\n                onConnect: onConnect,\n                onDrop: onDrop,\n                onDragOver: onDragOver,\n                nodeTypes: nodeTypes,\n                fitView: true,\n                className: \"bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_7__.Controls, {\n                        className: \"glass\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_8__.MiniMap, {\n                        className: \"glass\",\n                        nodeColor: (node)=>{\n                            switch(node.type){\n                                case \"agent\":\n                                    return \"#6366f1\";\n                                case \"task\":\n                                    return \"#8b5cf6\";\n                                default:\n                                    return \"#64748b\";\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_9__.Background, {\n                        variant: reactflow__WEBPACK_IMPORTED_MODULE_9__.BackgroundVariant.Dots,\n                        gap: 20,\n                        size: 1,\n                        color: \"#e2e8f0\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: ()=>setIsToolboxOpen(!isToolboxOpen),\n                className: \"absolute bottom-6 left-6 w-14 h-14 gradient-primary rounded-full shadow-lg flex items-center justify-center text-white z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    animate: {\n                        rotate: isToolboxOpen ? 45 : 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    children: \"+\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            isToolboxOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: -300\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                exit: {\n                    opacity: 0,\n                    x: -300\n                },\n                className: \"absolute left-6 bottom-24 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toolbox_panel__WEBPACK_IMPORTED_MODULE_5__.ToolboxPanel, {\n                    onClose: ()=>setIsToolboxOpen(false)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/workflow/workflow-canvas.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/workflow/workflow-canvas.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   formatTimeFromSeconds: () => (/* binding */ formatTimeFromSeconds),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatTimeFromSeconds(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n}\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + \"M\";\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + \"K\";\n    }\n    return num.toString();\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = seconds % 60;\n    if (hours > 0) {\n        return `${hours}h ${minutes}m ${remainingSeconds}s`;\n    } else if (minutes > 0) {\n        return `${minutes}m ${remainingSeconds}s`;\n    } else {\n        return `${remainingSeconds}s`;\n    }\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0Msc0JBQXNCQyxPQUFlO0lBQ25ELE1BQU1DLFVBQVVDLEtBQUtDLEtBQUssQ0FBQ0gsVUFBVTtJQUNyQyxNQUFNSSxtQkFBbUJKLFVBQVU7SUFDbkMsT0FBTyxDQUFDLEVBQUVDLFFBQVEsQ0FBQyxFQUFFRyxpQkFBaUJDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDO0FBQ3JFO0FBRU8sU0FBU0MsYUFBYUMsR0FBVztJQUN0QyxJQUFJQSxPQUFPLFNBQVM7UUFDbEIsT0FBTyxDQUFDQSxNQUFNLE9BQU0sRUFBR0MsT0FBTyxDQUFDLEtBQUs7SUFDdEM7SUFDQSxJQUFJRCxPQUFPLE1BQU07UUFDZixPQUFPLENBQUNBLE1BQU0sSUFBRyxFQUFHQyxPQUFPLENBQUMsS0FBSztJQUNuQztJQUNBLE9BQU9ELElBQUlILFFBQVE7QUFDckI7QUFFTyxTQUFTSyxXQUFXQyxJQUFtQjtJQUM1QyxPQUFPLElBQUlDLEtBQUtDLGNBQWMsQ0FBQyxTQUFTO1FBQ3RDQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsS0FBSztJQUNQLEdBQUdDLE1BQU0sQ0FBQyxJQUFJQyxLQUFLUDtBQUNyQjtBQUVPLFNBQVNRLFdBQVdSLElBQW1CO0lBQzVDLE9BQU8sSUFBSUMsS0FBS0MsY0FBYyxDQUFDLFNBQVM7UUFDdENPLE1BQU07UUFDTkMsUUFBUTtJQUNWLEdBQUdKLE1BQU0sQ0FBQyxJQUFJQyxLQUFLUDtBQUNyQjtBQUVPLFNBQVNXLGVBQWV0QixPQUFlO0lBQzVDLE1BQU11QixRQUFRckIsS0FBS0MsS0FBSyxDQUFDSCxVQUFVO0lBQ25DLE1BQU1DLFVBQVVDLEtBQUtDLEtBQUssQ0FBQyxVQUFXLE9BQVE7SUFDOUMsTUFBTUMsbUJBQW1CSixVQUFVO0lBRW5DLElBQUl1QixRQUFRLEdBQUc7UUFDYixPQUFPLENBQUMsRUFBRUEsTUFBTSxFQUFFLEVBQUV0QixRQUFRLEVBQUUsRUFBRUcsaUJBQWlCLENBQUMsQ0FBQztJQUNyRCxPQUFPLElBQUlILFVBQVUsR0FBRztRQUN0QixPQUFPLENBQUMsRUFBRUEsUUFBUSxFQUFFLEVBQUVHLGlCQUFpQixDQUFDLENBQUM7SUFDM0MsT0FBTztRQUNMLE9BQU8sQ0FBQyxFQUFFQSxpQkFBaUIsQ0FBQyxDQUFDO0lBQy9CO0FBQ0Y7QUFFTyxTQUFTb0IsYUFBYUMsSUFBWSxFQUFFQyxTQUFpQjtJQUMxRCxJQUFJRCxLQUFLRSxNQUFNLElBQUlELFdBQVcsT0FBT0Q7SUFDckMsT0FBT0EsS0FBS0csS0FBSyxDQUFDLEdBQUdGLGFBQWE7QUFDcEM7QUFFTyxTQUFTRztJQUNkLE9BQU8zQixLQUFLNEIsTUFBTSxHQUFHekIsUUFBUSxDQUFDLElBQUkwQixNQUFNLENBQUMsR0FBRztBQUM5QztBQUlPLFNBQVNDLFNBQ2RDLElBQU8sRUFDUEMsSUFBWTtJQUVaLElBQUlDLFVBQWlDO0lBRXJDLE9BQU8sQ0FBQyxHQUFHQztRQUNULElBQUlELFNBQVNFLGFBQWFGO1FBQzFCQSxVQUFVRyxXQUFXLElBQU1MLFFBQVFHLE9BQU9GO0lBQzVDO0FBQ0Y7QUFFTyxTQUFTSyxTQUNkTixJQUFPLEVBQ1BPLEtBQWE7SUFFYixJQUFJQztJQUVKLE9BQU8sQ0FBQyxHQUFHTDtRQUNULElBQUksQ0FBQ0ssWUFBWTtZQUNmUixRQUFRRztZQUNSSyxhQUFhO1lBQ2JILFdBQVcsSUFBTUcsYUFBYSxPQUFPRDtRQUN2QztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9saWIvdXRpbHMudHM/Zjc0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tICdjbHN4J1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJ1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0VGltZUZyb21TZWNvbmRzKHNlY29uZHM6IG51bWJlcik6IHN0cmluZyB7XG4gIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyA2MClcbiAgY29uc3QgcmVtYWluaW5nU2Vjb25kcyA9IHNlY29uZHMgJSA2MFxuICByZXR1cm4gYCR7bWludXRlc306JHtyZW1haW5pbmdTZWNvbmRzLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX1gXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXROdW1iZXIobnVtOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAobnVtID49IDEwMDAwMDApIHtcbiAgICByZXR1cm4gKG51bSAvIDEwMDAwMDApLnRvRml4ZWQoMSkgKyAnTSdcbiAgfVxuICBpZiAobnVtID49IDEwMDApIHtcbiAgICByZXR1cm4gKG51bSAvIDEwMDApLnRvRml4ZWQoMSkgKyAnSydcbiAgfVxuICByZXR1cm4gbnVtLnRvU3RyaW5nKClcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZTogc3RyaW5nIHwgRGF0ZSkge1xuICByZXR1cm4gbmV3IEludGwuRGF0ZVRpbWVGb3JtYXQoJ2VuLVVTJywge1xuICAgIHllYXI6ICdudW1lcmljJyxcbiAgICBtb250aDogJ2xvbmcnLFxuICAgIGRheTogJ251bWVyaWMnLFxuICB9KS5mb3JtYXQobmV3IERhdGUoZGF0ZSkpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRUaW1lKGRhdGU6IHN0cmluZyB8IERhdGUpIHtcbiAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KCdlbi1VUycsIHtcbiAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgbWludXRlOiAnMi1kaWdpdCcsXG4gIH0pLmZvcm1hdChuZXcgRGF0ZShkYXRlKSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdER1cmF0aW9uKHNlY29uZHM6IG51bWJlcikge1xuICBjb25zdCBob3VycyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDM2MDApXG4gIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKChzZWNvbmRzICUgMzYwMCkgLyA2MClcbiAgY29uc3QgcmVtYWluaW5nU2Vjb25kcyA9IHNlY29uZHMgJSA2MFxuXG4gIGlmIChob3VycyA+IDApIHtcbiAgICByZXR1cm4gYCR7aG91cnN9aCAke21pbnV0ZXN9bSAke3JlbWFpbmluZ1NlY29uZHN9c2BcbiAgfSBlbHNlIGlmIChtaW51dGVzID4gMCkge1xuICAgIHJldHVybiBgJHttaW51dGVzfW0gJHtyZW1haW5pbmdTZWNvbmRzfXNgXG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGAke3JlbWFpbmluZ1NlY29uZHN9c2BcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdHJ1bmNhdGVUZXh0KHRleHQ6IHN0cmluZywgbWF4TGVuZ3RoOiBudW1iZXIpIHtcbiAgaWYgKHRleHQubGVuZ3RoIDw9IG1heExlbmd0aCkgcmV0dXJuIHRleHRcbiAgcmV0dXJuIHRleHQuc2xpY2UoMCwgbWF4TGVuZ3RoKSArICcuLi4nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSlcbn1cblxuXG5cbmV4cG9ydCBmdW5jdGlvbiBkZWJvdW5jZTxUIGV4dGVuZHMgKC4uLmFyZ3M6IGFueVtdKSA9PiBhbnk+KFxuICBmdW5jOiBULFxuICB3YWl0OiBudW1iZXJcbik6ICguLi5hcmdzOiBQYXJhbWV0ZXJzPFQ+KSA9PiB2b2lkIHtcbiAgbGV0IHRpbWVvdXQ6IE5vZGVKUy5UaW1lb3V0IHwgbnVsbCA9IG51bGxcbiAgXG4gIHJldHVybiAoLi4uYXJnczogUGFyYW1ldGVyczxUPikgPT4ge1xuICAgIGlmICh0aW1lb3V0KSBjbGVhclRpbWVvdXQodGltZW91dClcbiAgICB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiBmdW5jKC4uLmFyZ3MpLCB3YWl0KVxuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB0aHJvdHRsZTxUIGV4dGVuZHMgKC4uLmFyZ3M6IGFueVtdKSA9PiBhbnk+KFxuICBmdW5jOiBULFxuICBsaW1pdDogbnVtYmVyXG4pOiAoLi4uYXJnczogUGFyYW1ldGVyczxUPikgPT4gdm9pZCB7XG4gIGxldCBpblRocm90dGxlOiBib29sZWFuXG4gIFxuICByZXR1cm4gKC4uLmFyZ3M6IFBhcmFtZXRlcnM8VD4pID0+IHtcbiAgICBpZiAoIWluVGhyb3R0bGUpIHtcbiAgICAgIGZ1bmMoLi4uYXJncylcbiAgICAgIGluVGhyb3R0bGUgPSB0cnVlXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IGluVGhyb3R0bGUgPSBmYWxzZSwgbGltaXQpXG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyIsImZvcm1hdFRpbWVGcm9tU2Vjb25kcyIsInNlY29uZHMiLCJtaW51dGVzIiwiTWF0aCIsImZsb29yIiwicmVtYWluaW5nU2Vjb25kcyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJmb3JtYXROdW1iZXIiLCJudW0iLCJ0b0ZpeGVkIiwiZm9ybWF0RGF0ZSIsImRhdGUiLCJJbnRsIiwiRGF0ZVRpbWVGb3JtYXQiLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJmb3JtYXQiLCJEYXRlIiwiZm9ybWF0VGltZSIsImhvdXIiLCJtaW51dGUiLCJmb3JtYXREdXJhdGlvbiIsImhvdXJzIiwidHJ1bmNhdGVUZXh0IiwidGV4dCIsIm1heExlbmd0aCIsImxlbmd0aCIsInNsaWNlIiwiZ2VuZXJhdGVJZCIsInJhbmRvbSIsInN1YnN0ciIsImRlYm91bmNlIiwiZnVuYyIsIndhaXQiLCJ0aW1lb3V0IiwiYXJncyIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJ0aHJvdHRsZSIsImxpbWl0IiwiaW5UaHJvdHRsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./store/workflow-store.ts":
/*!*********************************!*\
  !*** ./store/workflow-store.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWorkflowStore: () => (/* binding */ useWorkflowStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useWorkflowStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        selectedWorkflow: null,\n        isExecuting: false,\n        isExecutionPanelOpen: false,\n        executionStatus: \"idle\",\n        setSelectedWorkflow: (id)=>set({\n                selectedWorkflow: id\n            }),\n        toggleExecutionPanel: ()=>set((state)=>({\n                    isExecutionPanelOpen: !state.isExecutionPanelOpen\n                })),\n        startExecution: ()=>set({\n                isExecuting: true,\n                executionStatus: \"running\",\n                isExecutionPanelOpen: true\n            }),\n        pauseExecution: ()=>set({\n                executionStatus: \"paused\"\n            }),\n        stopExecution: ()=>set({\n                isExecuting: false,\n                executionStatus: \"idle\"\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./store/workflow-store.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"33e1a610ba87\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9hcHAvZ2xvYmFscy5jc3M/NjgyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzZTFhNjEwYmE4N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"CrewCraft AI Platform - Multi-Agent AI Orchestration\",\n    description: \"Create, manage, and deploy AI agent crews with Cerebras ultra-fast inference. Build sophisticated multi-agent workflows for complex tasks.\",\n    keywords: \"AI, CrewAI, Cerebras, Multi-Agent, Artificial Intelligence, Automation, Workflows\",\n    authors: [\n        {\n            name: \"CrewCraft Team\"\n        }\n    ],\n    openGraph: {\n        title: \"CrewCraft AI Platform\",\n        description: \"Enterprise-grade multi-agent AI orchestration platform\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-slate-50 text-slate-800\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            className: \"bg-white shadow-lg border border-slate-200\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/workflow/page.tsx":
/*!*******************************!*\
  !*** ./app/workflow/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e1),
/* harmony export */   useAppContext: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#useAppContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/recharts","vendor-chunks/lodash","vendor-chunks/tailwind-merge","vendor-chunks/d3-shape","vendor-chunks/react-smooth","vendor-chunks/decimal.js-light","vendor-chunks/d3-scale","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/lucide-react","vendor-chunks/fast-equals","vendor-chunks/d3-time-format","vendor-chunks/recharts-scale","vendor-chunks/d3-time","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-color","vendor-chunks/eventemitter3","vendor-chunks/react-is","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/d3-path","vendor-chunks/object-assign","vendor-chunks/internmap","vendor-chunks/@babel","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant","vendor-chunks/d3-selection","vendor-chunks/d3-transition","vendor-chunks/d3-zoom","vendor-chunks/d3-drag","vendor-chunks/@reactflow","vendor-chunks/d3-timer","vendor-chunks/d3-ease","vendor-chunks/d3-dispatch","vendor-chunks/classcat","vendor-chunks/reactflow"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fworkflow%2Fpage&page=%2Fworkflow%2Fpage&appPaths=%2Fworkflow%2Fpage&pagePath=private-next-app-dir%2Fworkflow%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();