/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZhcHAlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vPzE5ODYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGcmFqc2hhaCUyRkRvd25sb2FkcyUyRlByb2plY3RzJTJGQUlDcmV3RGVja2VyJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySmV0QnJhaW5zX01vbm8lMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1qZXRicmFpbnMtbW9ubyUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmpldGJyYWluc01vbm8lMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBNEc7QUFDNUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vP2MxYTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhanNoYWgvRG93bmxvYWRzL1Byb2plY3RzL0FJQ3Jld0RlY2tlci9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_layouts_dashboard_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layouts/dashboard-layout */ \"(ssr)/./components/layouts/dashboard-layout.tsx\");\n/* harmony import */ var _components_dashboard_stats_cards__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/stats-cards */ \"(ssr)/./components/dashboard/stats-cards.tsx\");\n/* harmony import */ var _components_dashboard_active_crews__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/active-crews */ \"(ssr)/./components/dashboard/active-crews.tsx\");\n/* harmony import */ var _components_dashboard_live_preview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/live-preview */ \"(ssr)/./components/dashboard/live-preview.tsx\");\n/* harmony import */ var _components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/quick-actions */ \"(ssr)/./components/dashboard/quick-actions.tsx\");\n/* harmony import */ var _components_dashboard_activity_feed__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/activity-feed */ \"(ssr)/./components/dashboard/activity-feed.tsx\");\n/* harmony import */ var _lib_hooks_use_dashboard_stats__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/hooks/use-dashboard-stats */ \"(ssr)/./lib/hooks/use-dashboard-stats.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { stats, isLoading } = (0,_lib_hooks_use_dashboard_stats__WEBPACK_IMPORTED_MODULE_7__.useDashboardStats)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_dashboard_layout__WEBPACK_IMPORTED_MODULE_1__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-slate-900\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mt-2\",\n                                    children: \"Monitor and manage your AI agent crews\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_5__.QuickActions, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_cards__WEBPACK_IMPORTED_MODULE_2__.StatsCards, {\n                        stats: stats,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.2\n                            },\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_active_crews__WEBPACK_IMPORTED_MODULE_3__.ActiveCrews, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_activity_feed__WEBPACK_IMPORTED_MODULE_6__.ActivityFeed, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.4\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_live_preview__WEBPACK_IMPORTED_MODULE_4__.LivePreview, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/active-crews.tsx":
/*!***********************************************!*\
  !*** ./components/dashboard/active-crews.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActiveCrews: () => (/* binding */ ActiveCrews)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,PauseIcon,PlayIcon,PlusIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,PauseIcon,PlayIcon,PlusIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,PauseIcon,PlayIcon,PlusIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PauseIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,PauseIcon,PlayIcon,PlusIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,PauseIcon,PlayIcon,PlusIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,EllipsisVerticalIcon,PauseIcon,PlayIcon,PlusIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _lib_hooks_use_active_crews__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hooks/use-active-crews */ \"(ssr)/./lib/hooks/use-active-crews.ts\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ ActiveCrews auto */ \n\n\n\n\n\n\nfunction ActiveCrews() {\n    const { crews, isLoading } = (0,_lib_hooks_use_active_crews__WEBPACK_IMPORTED_MODULE_2__.useActiveCrews)();\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"running\":\n                return \"bg-emerald-500\";\n            case \"pending\":\n                return \"bg-amber-500\";\n            case \"completed\":\n                return \"bg-slate-400\";\n            case \"failed\":\n                return \"bg-red-500\";\n            default:\n                return \"bg-slate-400\";\n        }\n    };\n    const getStatusText = (status)=>{\n        return status.charAt(0).toUpperCase() + status.slice(1);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl border border-slate-200 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-slate-900\",\n                        children: \"Active Crews\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-200 rounded-lg h-40\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl border border-slate-200 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-slate-900\",\n                        children: \"Active Crews\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/crews/new\",\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            \"New Crew\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: crews.map((crew, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"group relative bg-slate-50 rounded-lg p-4 hover:bg-slate-100 transition-colors cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"h-3 w-3 rounded-full\", getStatusColor(crew.status), crew.status === \"running\" && \"animate-pulse\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-slate-600\",\n                                                children: getStatusText(crew.status)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                        as: \"div\",\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Button, {\n                                                className: \"p-1 rounded-md hover:bg-slate-200 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 text-slate-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Items, {\n                                                className: \"absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50\",\n                                                            children: \"View Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50\",\n                                                            children: \"Edit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_headlessui_react__WEBPACK_IMPORTED_MODULE_6__.Menu.Item, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-slate-50\",\n                                                            children: \"Delete\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-slate-200 rounded-full h-1 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"h-1 rounded-full transition-all duration-300\", crew.status === \"running\" ? \"bg-primary-600\" : crew.status === \"completed\" ? \"bg-emerald-500\" : crew.status === \"failed\" ? \"bg-red-500\" : \"bg-amber-500\"),\n                                    style: {\n                                        width: `${crew.progress}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-base font-semibold text-slate-900 mb-2\",\n                                        children: crew.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-600 mb-2\",\n                                        children: [\n                                            crew.agents?.length || 0,\n                                            \" agents • \",\n                                            crew.tasks?.length || 0,\n                                            \" tasks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    crew.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2 py-1 rounded-md bg-blue-100 text-blue-800 text-xs font-medium\",\n                                        children: crew.model\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    crew.status === \"running\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 rounded-md bg-amber-100 text-amber-600 hover:bg-amber-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 rounded-md bg-emerald-100 text-emerald-600 hover:bg-emerald-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 rounded-md bg-red-100 text-red-600 hover:bg-red-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: `/crews/${crew.id}`,\n                                        className: \"ml-auto text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                                        children: \"View Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 rounded-lg border-2 border-transparent group-hover:border-primary-200 transition-colors pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, crew.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            crews.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 text-slate-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-slate-900 mb-2\",\n                        children: \"No active crews\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-600 mb-4\",\n                        children: \"Get started by creating your first AI crew\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/crews/new\",\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_EllipsisVerticalIcon_PauseIcon_PlayIcon_PlusIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            \"Create Your First Crew\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/active-crews.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/active-crews.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/activity-feed.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/activity-feed.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivityFeed: () => (/* binding */ ActivityFeed)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(ssr)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CpuChipIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CpuChipIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CpuChipIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CpuChipIcon,ExclamationTriangleIcon,InformationCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _lib_hooks_use_activity_feed__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/hooks/use-activity-feed */ \"(ssr)/./lib/hooks/use-activity-feed.ts\");\n/* __next_internal_client_entry_do_not_use__ ActivityFeed auto */ \n\n\n\n\nfunction ActivityFeed() {\n    const { activities, isLoading } = (0,_lib_hooks_use_activity_feed__WEBPACK_IMPORTED_MODULE_1__.useActivityFeed)();\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n            case \"warning\":\n                return _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"info\":\n                return _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return _barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        }\n    };\n    const getActivityColor = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"text-emerald-600 bg-emerald-100\";\n            case \"warning\":\n                return \"text-amber-600 bg-amber-100\";\n            case \"error\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-blue-600 bg-blue-100\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl border border-slate-200 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-slate-900 mb-6\",\n                    children: \"Activity Feed\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        ...Array(5)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-slate-200 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-slate-200 rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-slate-200 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl border border-slate-200 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-slate-900 mb-6\",\n                children: \"Activity Feed\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: activities.map((activity, index)=>{\n                    const Icon = getActivityIcon(activity.type);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        className: \"flex items-start gap-3 p-3 rounded-lg hover:bg-slate-50 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-2 rounded-full ${getActivityColor(activity.type)}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-slate-900 mb-1\",\n                                        children: activity.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-slate-600 mb-2\",\n                                        children: activity.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-xs text-slate-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(new Date(activity.timestamp)),\n                                                    \" ago\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            activity.crewName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: activity.crewName\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, activity.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            activities.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CpuChipIcon_ExclamationTriangleIcon_InformationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-12 w-12 text-slate-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-500\",\n                        children: \"No recent activity\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/activity-feed.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/activity-feed.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/live-preview.tsx":
/*!***********************************************!*\
  !*** ./components/dashboard/live-preview.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LivePreview: () => (/* binding */ LivePreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronUpIcon,PlayIcon,StopIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-syntax-highlighter/dist/cjs/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/cjs/styles/prism/index.js\");\n/* harmony import */ var _lib_hooks_use_live_preview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hooks/use-live-preview */ \"(ssr)/./lib/hooks/use-live-preview.ts\");\n/* __next_internal_client_entry_do_not_use__ LivePreview auto */ \n\n\n\n\n\n\nfunction LivePreview() {\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { logs, isRunning, currentCrew, startPreview, stopPreview } = (0,_lib_hooks_use_live_preview__WEBPACK_IMPORTED_MODULE_2__.useLivePreview)();\n    const formatLogMessage = (message)=>{\n        // Check if message contains code or structured data\n        try {\n            const parsed = JSON.parse(message);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                language: \"json\",\n                style: react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_4__.oneLight,\n                customStyle: {\n                    background: \"transparent\",\n                    padding: \"0\",\n                    margin: \"0\",\n                    fontSize: \"12px\"\n                },\n                children: JSON.stringify(parsed, null, 2)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this);\n        } catch  {\n            // Check if it's a Python code snippet\n            if (message.includes(\"def \") || message.includes(\"import \") || message.includes(\"class \")) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    language: \"python\",\n                    style: react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_4__.oneLight,\n                    customStyle: {\n                        background: \"transparent\",\n                        padding: \"0\",\n                        margin: \"0\",\n                        fontSize: \"12px\"\n                    },\n                    children: message\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-slate-600\",\n                children: message\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl border border-slate-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-slate-200 bg-slate-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-slate-900\",\n                                children: \"Live Preview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            currentCrew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-md bg-blue-100 text-blue-800 text-xs font-medium\",\n                                children: currentCrew\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            isRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                animate: {\n                                    scale: [\n                                        1,\n                                        1.2,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 1,\n                                    repeat: Infinity\n                                },\n                                className: \"h-2 w-2 bg-emerald-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 bg-white rounded-lg border border-slate-200 p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: isRunning ? stopPreview : startPreview,\n                                    className: `p-2 rounded-md transition-colors ${isRunning ? \"bg-red-100 text-red-600 hover:bg-red-200\" : \"bg-emerald-100 text-emerald-600 hover:bg-emerald-200\"}`,\n                                    children: isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                className: \"p-2 rounded-md hover:bg-slate-100 transition-colors\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 text-slate-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 text-slate-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        height: 0\n                    },\n                    animate: {\n                        height: \"auto\"\n                    },\n                    exit: {\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-slate-50 h-80 overflow-y-auto font-mono text-sm custom-scrollbar\",\n                        children: logs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-slate-200 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronUpIcon_PlayIcon_StopIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-slate-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-500 mb-2\",\n                                        children: \"No active preview session\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-400\",\n                                        children: \"Start a crew to see live execution logs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"flex items-start gap-3 p-2 rounded-md hover:bg-white/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-slate-400 font-medium min-w-0 flex-shrink-0\",\n                                            children: log.timestamp\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-xs font-medium min-w-0 flex-shrink-0 ${log.level === \"error\" ? \"text-red-600\" : log.level === \"warning\" ? \"text-amber-600\" : log.level === \"success\" ? \"text-emerald-600\" : \"text-blue-600\"}`,\n                                            children: [\n                                                \"[\",\n                                                log.agent,\n                                                \"]\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: formatLogMessage(log.message)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 21\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/live-preview.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2Rhc2hib2FyZC9saXZlLXByZXZpZXcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNZO0FBT25CO0FBQ2lDO0FBQ0k7QUFDWjtBQUV0RCxTQUFTVztJQUNkLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHYiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLEVBQUVjLElBQUksRUFBRUMsU0FBUyxFQUFFQyxXQUFXLEVBQUVDLFlBQVksRUFBRUMsV0FBVyxFQUFFLEdBQUdSLDJFQUFjQTtJQUVsRixNQUFNUyxtQkFBbUIsQ0FBQ0M7UUFDeEIsb0RBQW9EO1FBQ3BELElBQUk7WUFDRixNQUFNQyxTQUFTQyxLQUFLQyxLQUFLLENBQUNIO1lBQzFCLHFCQUNFLDhEQUFDWixnRUFBaUJBO2dCQUNoQmdCLFVBQVM7Z0JBQ1RDLE9BQU9oQixvRkFBUUE7Z0JBQ2ZpQixhQUFhO29CQUNYQyxZQUFZO29CQUNaQyxTQUFTO29CQUNUQyxRQUFRO29CQUNSQyxVQUFVO2dCQUNaOzBCQUVDUixLQUFLUyxTQUFTLENBQUNWLFFBQVEsTUFBTTs7Ozs7O1FBR3BDLEVBQUUsT0FBTTtZQUNOLHNDQUFzQztZQUN0QyxJQUFJRCxRQUFRWSxRQUFRLENBQUMsV0FBV1osUUFBUVksUUFBUSxDQUFDLGNBQWNaLFFBQVFZLFFBQVEsQ0FBQyxXQUFXO2dCQUN6RixxQkFDRSw4REFBQ3hCLGdFQUFpQkE7b0JBQ2hCZ0IsVUFBUztvQkFDVEMsT0FBT2hCLG9GQUFRQTtvQkFDZmlCLGFBQWE7d0JBQ1hDLFlBQVk7d0JBQ1pDLFNBQVM7d0JBQ1RDLFFBQVE7d0JBQ1JDLFVBQVU7b0JBQ1o7OEJBRUNWOzs7Ozs7WUFHUDtZQUNBLHFCQUFPLDhEQUFDYTtnQkFBS0MsV0FBVTswQkFBa0JkOzs7Ozs7UUFDM0M7SUFDRjtJQUVBLHFCQUNFLDhEQUFDZTtRQUFJRCxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFHRixXQUFVOzBDQUF1Qzs7Ozs7OzRCQUNwRGxCLDZCQUNDLDhEQUFDaUI7Z0NBQUtDLFdBQVU7MENBQ2JsQjs7Ozs7OzRCQUdKRCwyQkFDQyw4REFBQ2QsaURBQU1BLENBQUNrQyxHQUFHO2dDQUNURSxTQUFTO29DQUFFQyxPQUFPO3dDQUFDO3dDQUFHO3dDQUFLO3FDQUFFO2dDQUFDO2dDQUM5QkMsWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBR0MsUUFBUUM7Z0NBQVM7Z0NBQzVDUixXQUFVOzs7Ozs7Ozs7Ozs7a0NBS2hCLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBRWIsOERBQUNDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDUztvQ0FDQ0MsU0FBUzdCLFlBQVlHLGNBQWNEO29DQUNuQ2lCLFdBQVcsQ0FBQyxpQ0FBaUMsRUFDM0NuQixZQUNJLDZDQUNBLHVEQUNMLENBQUM7OENBRURBLDBCQUNDLDhEQUFDWCx5SUFBUUE7d0NBQUM4QixXQUFVOzs7Ozs2REFFcEIsOERBQUMvQix5SUFBUUE7d0NBQUMrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBDQU0xQiw4REFBQ1M7Z0NBQ0NDLFNBQVMsSUFBTS9CLGNBQWMsQ0FBQ0Q7Z0NBQzlCc0IsV0FBVTswQ0FFVHRCLDJCQUNDLDhEQUFDUCx5SUFBYUE7b0NBQUM2QixXQUFVOzs7Ozt5REFFekIsOERBQUM1Qix5SUFBZUE7b0NBQUM0QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPbkMsOERBQUNoQywyREFBZUE7MEJBQ2JVLDRCQUNDLDhEQUFDWCxpREFBTUEsQ0FBQ2tDLEdBQUc7b0JBQ1RVLFNBQVM7d0JBQUVDLFFBQVE7b0JBQUU7b0JBQ3JCVCxTQUFTO3dCQUFFUyxRQUFRO29CQUFPO29CQUMxQkMsTUFBTTt3QkFBRUQsUUFBUTtvQkFBRTtvQkFDbEJQLFlBQVk7d0JBQUVDLFVBQVU7b0JBQUk7b0JBQzVCTixXQUFVOzhCQUVWLDRFQUFDQzt3QkFBSUQsV0FBVTtrQ0FDWnBCLEtBQUtrQyxNQUFNLEtBQUssa0JBQ2YsOERBQUNiOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDL0IseUlBQVFBOzRDQUFDK0IsV0FBVTs7Ozs7Ozs7Ozs7a0RBRXRCLDhEQUFDZTt3Q0FBRWYsV0FBVTtrREFBc0I7Ozs7OztrREFDbkMsOERBQUNlO3dDQUFFZixXQUFVO2tEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7OztpREFJMUMsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNacEIsS0FBS29DLEdBQUcsQ0FBQyxDQUFDQyxLQUFLQyxzQkFDZCw4REFBQ25ELGlEQUFNQSxDQUFDa0MsR0FBRztvQ0FFVFUsU0FBUzt3Q0FBRVEsU0FBUzt3Q0FBR0MsR0FBRyxDQUFDO29DQUFHO29DQUM5QmpCLFNBQVM7d0NBQUVnQixTQUFTO3dDQUFHQyxHQUFHO29DQUFFO29DQUM1QnBCLFdBQVU7O3NEQUVWLDhEQUFDRDs0Q0FBS0MsV0FBVTtzREFDYmlCLElBQUlJLFNBQVM7Ozs7OztzREFFaEIsOERBQUN0Qjs0Q0FBS0MsV0FBVyxDQUFDLDBDQUEwQyxFQUMxRGlCLElBQUlLLEtBQUssS0FBSyxVQUFVLGlCQUN4QkwsSUFBSUssS0FBSyxLQUFLLFlBQVksbUJBQzFCTCxJQUFJSyxLQUFLLEtBQUssWUFBWSxxQkFDMUIsZ0JBQ0QsQ0FBQzs7Z0RBQUU7Z0RBQ0FMLElBQUlNLEtBQUs7Z0RBQUM7Ozs7Ozs7c0RBRWQsOERBQUN0Qjs0Q0FBSUQsV0FBVTtzREFDWmYsaUJBQWlCZ0MsSUFBSS9CLE9BQU87Ozs7Ozs7bUNBakIxQmdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBNkIzQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvZGFzaGJvYXJkL2xpdmUtcHJldmlldy50c3g/NDc0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgXG4gIFBsYXlJY29uLCBcbiAgUGF1c2VJY29uLCBcbiAgU3RvcEljb24sXG4gIENoZXZyb25VcEljb24sXG4gIENoZXZyb25Eb3duSWNvbiBcbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJ1xuaW1wb3J0IHsgUHJpc20gYXMgU3ludGF4SGlnaGxpZ2h0ZXIgfSBmcm9tICdyZWFjdC1zeW50YXgtaGlnaGxpZ2h0ZXInXG5pbXBvcnQgeyBvbmVMaWdodCB9IGZyb20gJ3JlYWN0LXN5bnRheC1oaWdobGlnaHRlci9kaXN0L2Nqcy9zdHlsZXMvcHJpc20nXG5pbXBvcnQgeyB1c2VMaXZlUHJldmlldyB9IGZyb20gJ0AvbGliL2hvb2tzL3VzZS1saXZlLXByZXZpZXcnXG5cbmV4cG9ydCBmdW5jdGlvbiBMaXZlUHJldmlldygpIHtcbiAgY29uc3QgW2lzRXhwYW5kZWQsIHNldElzRXhwYW5kZWRdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgeyBsb2dzLCBpc1J1bm5pbmcsIGN1cnJlbnRDcmV3LCBzdGFydFByZXZpZXcsIHN0b3BQcmV2aWV3IH0gPSB1c2VMaXZlUHJldmlldygpXG5cbiAgY29uc3QgZm9ybWF0TG9nTWVzc2FnZSA9IChtZXNzYWdlOiBzdHJpbmcpID0+IHtcbiAgICAvLyBDaGVjayBpZiBtZXNzYWdlIGNvbnRhaW5zIGNvZGUgb3Igc3RydWN0dXJlZCBkYXRhXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UobWVzc2FnZSlcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxTeW50YXhIaWdobGlnaHRlclxuICAgICAgICAgIGxhbmd1YWdlPVwianNvblwiXG4gICAgICAgICAgc3R5bGU9e29uZUxpZ2h0fVxuICAgICAgICAgIGN1c3RvbVN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgcGFkZGluZzogJzAnLFxuICAgICAgICAgICAgbWFyZ2luOiAnMCcsXG4gICAgICAgICAgICBmb250U2l6ZTogJzEycHgnLFxuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICB7SlNPTi5zdHJpbmdpZnkocGFyc2VkLCBudWxsLCAyKX1cbiAgICAgICAgPC9TeW50YXhIaWdobGlnaHRlcj5cbiAgICAgIClcbiAgICB9IGNhdGNoIHtcbiAgICAgIC8vIENoZWNrIGlmIGl0J3MgYSBQeXRob24gY29kZSBzbmlwcGV0XG4gICAgICBpZiAobWVzc2FnZS5pbmNsdWRlcygnZGVmICcpIHx8IG1lc3NhZ2UuaW5jbHVkZXMoJ2ltcG9ydCAnKSB8fCBtZXNzYWdlLmluY2x1ZGVzKCdjbGFzcyAnKSkge1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxTeW50YXhIaWdobGlnaHRlclxuICAgICAgICAgICAgbGFuZ3VhZ2U9XCJweXRob25cIlxuICAgICAgICAgICAgc3R5bGU9e29uZUxpZ2h0fVxuICAgICAgICAgICAgY3VzdG9tU3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzAnLFxuICAgICAgICAgICAgICBtYXJnaW46ICcwJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxMnB4JyxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAge21lc3NhZ2V9XG4gICAgICAgICAgPC9TeW50YXhIaWdobGlnaHRlcj5cbiAgICAgICAgKVxuICAgICAgfVxuICAgICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNjAwXCI+e21lc3NhZ2V9PC9zcGFuPlxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXItYiBib3JkZXItc2xhdGUtMjAwIGJnLXNsYXRlLTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtc2xhdGUtOTAwXCI+TGl2ZSBQcmV2aWV3PC9oMz5cbiAgICAgICAgICB7Y3VycmVudENyZXcgJiYgKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkLW1kIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgdGV4dC14cyBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICB7Y3VycmVudENyZXd9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgKX1cbiAgICAgICAgICB7aXNSdW5uaW5nICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IFsxLCAxLjIsIDFdIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDEsIHJlcGVhdDogSW5maW5pdHkgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0yIHctMiBiZy1lbWVyYWxkLTUwMCByb3VuZGVkLWZ1bGxcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgey8qIENvbnRyb2wgQnV0dG9ucyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIGJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1zbGF0ZS0yMDAgcC0xXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2lzUnVubmluZyA/IHN0b3BQcmV2aWV3IDogc3RhcnRQcmV2aWV3fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGlzUnVubmluZyBcbiAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNjAwIGhvdmVyOmJnLXJlZC0yMDAnIFxuICAgICAgICAgICAgICAgICAgOiAnYmctZW1lcmFsZC0xMDAgdGV4dC1lbWVyYWxkLTYwMCBob3ZlcjpiZy1lbWVyYWxkLTIwMCdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc1J1bm5pbmcgPyAoXG4gICAgICAgICAgICAgICAgPFN0b3BJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxQbGF5SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEV4cGFuZC9Db2xsYXBzZSAqL31cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0V4cGFuZGVkKCFpc0V4cGFuZGVkKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLW1kIGhvdmVyOmJnLXNsYXRlLTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzRXhwYW5kZWQgPyAoXG4gICAgICAgICAgICAgIDxDaGV2cm9uVXBJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1zbGF0ZS00MDBcIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtc2xhdGUtNDAwXCIgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAge2lzRXhwYW5kZWQgJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IGhlaWdodDogMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgZXhpdD17eyBoZWlnaHQ6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1zbGF0ZS01MCBoLTgwIG92ZXJmbG93LXktYXV0byBmb250LW1vbm8gdGV4dC1zbSBjdXN0b20tc2Nyb2xsYmFyXCI+XG4gICAgICAgICAgICAgIHtsb2dzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1zbGF0ZS0yMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UGxheUljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXNsYXRlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTUwMCBtYi0yXCI+Tm8gYWN0aXZlIHByZXZpZXcgc2Vzc2lvbjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTQwMFwiPlN0YXJ0IGEgY3JldyB0byBzZWUgbGl2ZSBleGVjdXRpb24gbG9nczwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICB7bG9ncy5tYXAoKGxvZywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTEwIH19XG4gICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMyBwLTIgcm91bmRlZC1tZCBob3ZlcjpiZy13aGl0ZS81MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNDAwIGZvbnQtbWVkaXVtIG1pbi13LTAgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xvZy50aW1lc3RhbXB9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgZm9udC1tZWRpdW0gbWluLXctMCBmbGV4LXNocmluay0wICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBsb2cubGV2ZWwgPT09ICdlcnJvcicgPyAndGV4dC1yZWQtNjAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICBsb2cubGV2ZWwgPT09ICd3YXJuaW5nJyA/ICd0ZXh0LWFtYmVyLTYwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgbG9nLmxldmVsID09PSAnc3VjY2VzcycgPyAndGV4dC1lbWVyYWxkLTYwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgJ3RleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgW3tsb2cuYWdlbnR9XVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0TG9nTWVzc2FnZShsb2cubWVzc2FnZSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIlBsYXlJY29uIiwiU3RvcEljb24iLCJDaGV2cm9uVXBJY29uIiwiQ2hldnJvbkRvd25JY29uIiwiUHJpc20iLCJTeW50YXhIaWdobGlnaHRlciIsIm9uZUxpZ2h0IiwidXNlTGl2ZVByZXZpZXciLCJMaXZlUHJldmlldyIsImlzRXhwYW5kZWQiLCJzZXRJc0V4cGFuZGVkIiwibG9ncyIsImlzUnVubmluZyIsImN1cnJlbnRDcmV3Iiwic3RhcnRQcmV2aWV3Iiwic3RvcFByZXZpZXciLCJmb3JtYXRMb2dNZXNzYWdlIiwibWVzc2FnZSIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsImxhbmd1YWdlIiwic3R5bGUiLCJjdXN0b21TdHlsZSIsImJhY2tncm91bmQiLCJwYWRkaW5nIiwibWFyZ2luIiwiZm9udFNpemUiLCJzdHJpbmdpZnkiLCJpbmNsdWRlcyIsInNwYW4iLCJjbGFzc05hbWUiLCJkaXYiLCJoMyIsImFuaW1hdGUiLCJzY2FsZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsInJlcGVhdCIsIkluZmluaXR5IiwiYnV0dG9uIiwib25DbGljayIsImluaXRpYWwiLCJoZWlnaHQiLCJleGl0IiwibGVuZ3RoIiwicCIsIm1hcCIsImxvZyIsImluZGV4Iiwib3BhY2l0eSIsIngiLCJ0aW1lc3RhbXAiLCJsZXZlbCIsImFnZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/live-preview.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/quick-actions.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/quick-actions.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: () => (/* binding */ QuickActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentDuplicateIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentDuplicateIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentDuplicateIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,Cog6ToothIcon,DocumentDuplicateIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \n\n\n\nconst actions = [\n    {\n        name: \"New Crew\",\n        href: \"/crews/new\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"primary\"\n    },\n    {\n        name: \"Templates\",\n        href: \"/templates\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"slate\"\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"slate\"\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ChartBarIcon_Cog6ToothIcon_DocumentDuplicateIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"slate\"\n    }\n];\nfunction QuickActions() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: actions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: 20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                transition: {\n                    delay: index * 0.1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: action.href,\n                    className: `inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${action.color === \"primary\" ? \"bg-primary-600 text-white border-primary-600 hover:bg-primary-700\" : \"bg-white text-slate-700 border-slate-200 hover:bg-slate-50\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/quick-actions.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: action.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/quick-actions.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/quick-actions.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, this)\n            }, action.name, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/quick-actions.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/quick-actions.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/quick-actions.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/stats-cards.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/stats-cards.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCards: () => (/* binding */ StatsCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckCircleIcon,CpuChipIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckCircleIcon,CpuChipIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckCircleIcon,CpuChipIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ StatsCards auto */ \n\n\nfunction StatsCards({ stats, isLoading }) {\n    const cards = [\n        {\n            name: \"Active Crews\",\n            value: stats?.activeCrews || 0,\n            icon: _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            color: \"emerald\",\n            change: \"+12%\",\n            changeType: \"positive\"\n        },\n        {\n            name: \"Total Tasks\",\n            value: stats?.totalTasks || 0,\n            icon: _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"blue\",\n            change: \"+19%\",\n            changeType: \"positive\"\n        },\n        {\n            name: \"Success Rate\",\n            value: `${stats?.successRate || 0}%`,\n            icon: _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"green\",\n            change: \"+2.1%\",\n            changeType: \"positive\"\n        },\n        {\n            name: \"Avg Speed\",\n            value: `${stats?.averageSpeed || 0}s`,\n            icon: _barrel_optimize_names_BoltIcon_CheckCircleIcon_CpuChipIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"purple\",\n            change: \"-0.3s\",\n            changeType: \"positive\"\n        }\n    ];\n    const colorClasses = {\n        emerald: \"text-emerald-600 bg-emerald-100\",\n        blue: \"text-blue-600 bg-blue-100\",\n        green: \"text-green-600 bg-green-100\",\n        purple: \"text-purple-600 bg-purple-100\"\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n            children: [\n                ...Array(4)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl border border-slate-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-slate-200 rounded w-20 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-slate-200 rounded w-16 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-slate-200 rounded w-12\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this)\n                }, i, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\",\n        children: cards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: index * 0.1\n                },\n                className: \"bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-shadow duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-slate-600\",\n                                    children: card.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-slate-900 mt-2\",\n                                    children: card.value\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm font-medium ${card.changeType === \"positive\" ? \"text-emerald-600\" : \"text-red-600\"}`,\n                                            children: card.change\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-slate-500 ml-1\",\n                                            children: \"vs last month\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `p-3 rounded-lg ${colorClasses[card.color]}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(card.icon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, card.name, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/dashboard/stats-cards.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2Rhc2hib2FyZC9zdGF0cy1jYXJkcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFc0M7QUFNRjtBQVE3QixTQUFTSSxXQUFXLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFtQjtJQUM5RCxNQUFNQyxRQUFRO1FBQ1o7WUFDRUMsTUFBTTtZQUNOQyxPQUFPSixPQUFPSyxlQUFlO1lBQzdCQyxNQUFNViw4SEFBV0E7WUFDakJXLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxZQUFZO1FBQ2Q7UUFDQTtZQUNFTixNQUFNO1lBQ05DLE9BQU9KLE9BQU9VLGNBQWM7WUFDNUJKLE1BQU1ULDhIQUFlQTtZQUNyQlUsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFlBQVk7UUFDZDtRQUNBO1lBQ0VOLE1BQU07WUFDTkMsT0FBTyxDQUFDLEVBQUVKLE9BQU9XLGVBQWUsRUFBRSxDQUFDLENBQUM7WUFDcENMLE1BQU1ULDhIQUFlQTtZQUNyQlUsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFlBQVk7UUFDZDtRQUNBO1lBQ0VOLE1BQU07WUFDTkMsT0FBTyxDQUFDLEVBQUVKLE9BQU9ZLGdCQUFnQixFQUFFLENBQUMsQ0FBQztZQUNyQ04sTUFBTVIsOEhBQVFBO1lBQ2RTLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxZQUFZO1FBQ2Q7S0FDRDtJQUVELE1BQU1JLGVBQXVDO1FBQzNDQyxTQUFTO1FBQ1RDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRO0lBQ1Y7SUFFQSxJQUFJaEIsV0FBVztRQUNiLHFCQUNFLDhEQUFDaUI7WUFBSUMsV0FBVTtzQkFDWjttQkFBSUMsTUFBTTthQUFHLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDckIsOERBQUNMO29CQUFZQyxXQUFVOzhCQUNyQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7O21CQUpUSTs7Ozs7Ozs7OztJQVVsQjtJQUVBLHFCQUNFLDhEQUFDTDtRQUFJQyxXQUFVO2tCQUNaakIsTUFBTW1CLEdBQUcsQ0FBQyxDQUFDRyxNQUFNQyxzQkFDaEIsOERBQUM5QixpREFBTUEsQ0FBQ3VCLEdBQUc7Z0JBRVRRLFNBQVM7b0JBQUVDLFNBQVM7b0JBQUdDLEdBQUc7Z0JBQUc7Z0JBQzdCQyxTQUFTO29CQUFFRixTQUFTO29CQUFHQyxHQUFHO2dCQUFFO2dCQUM1QkUsWUFBWTtvQkFBRUMsT0FBT04sUUFBUTtnQkFBSTtnQkFDakNOLFdBQVU7MEJBRVYsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYTtvQ0FBRWIsV0FBVTs4Q0FBc0NLLEtBQUtyQixJQUFJOzs7Ozs7OENBQzVELDhEQUFDNkI7b0NBQUViLFdBQVU7OENBQTBDSyxLQUFLcEIsS0FBSzs7Ozs7OzhDQUNqRSw4REFBQ2M7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBS2QsV0FBVyxDQUFDLG9CQUFvQixFQUNwQ0ssS0FBS2YsVUFBVSxLQUFLLGFBQWEscUJBQXFCLGVBQ3ZELENBQUM7c0RBQ0NlLEtBQUtoQixNQUFNOzs7Ozs7c0RBRWQsOERBQUN5Qjs0Q0FBS2QsV0FBVTtzREFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHbEQsOERBQUNEOzRCQUFJQyxXQUFXLENBQUMsZUFBZSxFQUFFTixZQUFZLENBQUNXLEtBQUtqQixLQUFLLENBQUMsQ0FBQyxDQUFDO3NDQUMxRCw0RUFBQ2lCLEtBQUtsQixJQUFJO2dDQUFDYSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztlQXBCcEJLLEtBQUtyQixJQUFJOzs7Ozs7Ozs7O0FBMkJ4QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvZGFzaGJvYXJkL3N0YXRzLWNhcmRzLnRzeD8zZmZmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgXG4gIENwdUNoaXBJY29uLCBcbiAgQ2hlY2tDaXJjbGVJY29uLCBcbiAgQ2xvY2tJY29uLCBcbiAgQm9sdEljb24gXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSdcbmltcG9ydCB7IERhc2hib2FyZFN0YXRzIH0gZnJvbSAnQC90eXBlcy9kYXNoYm9hcmQnXG5cbmludGVyZmFjZSBTdGF0c0NhcmRzUHJvcHMge1xuICBzdGF0czogRGFzaGJvYXJkU3RhdHMgfCBudWxsXG4gIGlzTG9hZGluZzogYm9vbGVhblxufVxuXG5leHBvcnQgZnVuY3Rpb24gU3RhdHNDYXJkcyh7IHN0YXRzLCBpc0xvYWRpbmcgfTogU3RhdHNDYXJkc1Byb3BzKSB7XG4gIGNvbnN0IGNhcmRzID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICdBY3RpdmUgQ3Jld3MnLFxuICAgICAgdmFsdWU6IHN0YXRzPy5hY3RpdmVDcmV3cyB8fCAwLFxuICAgICAgaWNvbjogQ3B1Q2hpcEljb24sXG4gICAgICBjb2xvcjogJ2VtZXJhbGQnLFxuICAgICAgY2hhbmdlOiAnKzEyJScsXG4gICAgICBjaGFuZ2VUeXBlOiAncG9zaXRpdmUnIGFzIGNvbnN0LFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ1RvdGFsIFRhc2tzJyxcbiAgICAgIHZhbHVlOiBzdGF0cz8udG90YWxUYXNrcyB8fCAwLFxuICAgICAgaWNvbjogQ2hlY2tDaXJjbGVJY29uLFxuICAgICAgY29sb3I6ICdibHVlJyxcbiAgICAgIGNoYW5nZTogJysxOSUnLFxuICAgICAgY2hhbmdlVHlwZTogJ3Bvc2l0aXZlJyBhcyBjb25zdCxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICdTdWNjZXNzIFJhdGUnLFxuICAgICAgdmFsdWU6IGAke3N0YXRzPy5zdWNjZXNzUmF0ZSB8fCAwfSVgLFxuICAgICAgaWNvbjogQ2hlY2tDaXJjbGVJY29uLFxuICAgICAgY29sb3I6ICdncmVlbicsXG4gICAgICBjaGFuZ2U6ICcrMi4xJScsXG4gICAgICBjaGFuZ2VUeXBlOiAncG9zaXRpdmUnIGFzIGNvbnN0LFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0F2ZyBTcGVlZCcsXG4gICAgICB2YWx1ZTogYCR7c3RhdHM/LmF2ZXJhZ2VTcGVlZCB8fCAwfXNgLFxuICAgICAgaWNvbjogQm9sdEljb24sXG4gICAgICBjb2xvcjogJ3B1cnBsZScsXG4gICAgICBjaGFuZ2U6ICctMC4zcycsXG4gICAgICBjaGFuZ2VUeXBlOiAncG9zaXRpdmUnIGFzIGNvbnN0LFxuICAgIH0sXG4gIF1cblxuICBjb25zdCBjb2xvckNsYXNzZXM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgZW1lcmFsZDogJ3RleHQtZW1lcmFsZC02MDAgYmctZW1lcmFsZC0xMDAnLFxuICAgIGJsdWU6ICd0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtMTAwJyxcbiAgICBncmVlbjogJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTEwMCcsXG4gICAgcHVycGxlOiAndGV4dC1wdXJwbGUtNjAwIGJnLXB1cnBsZS0xMDAnLFxuICB9XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTYgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTRcIj5cbiAgICAgICAge1suLi5BcnJheSg0KV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci1zbGF0ZS0yMDAgcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctc2xhdGUtMjAwIHJvdW5kZWQgdy0yMCBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLXNsYXRlLTIwMCByb3VuZGVkIHctMTYgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1zbGF0ZS0yMDAgcm91bmRlZCB3LTEyXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNiBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNFwiPlxuICAgICAge2NhcmRzLm1hcCgoY2FyZCwgaW5kZXgpID0+IChcbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBrZXk9e2NhcmQubmFtZX1cbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIHAtNiBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc2xhdGUtNjAwXCI+e2NhcmQubmFtZX08L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTkwMCBtdC0yXCI+e2NhcmQudmFsdWV9PC9wPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICBjYXJkLmNoYW5nZVR5cGUgPT09ICdwb3NpdGl2ZScgPyAndGV4dC1lbWVyYWxkLTYwMCcgOiAndGV4dC1yZWQtNjAwJ1xuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgIHtjYXJkLmNoYW5nZX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTUwMCBtbC0xXCI+dnMgbGFzdCBtb250aDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQtbGcgJHtjb2xvckNsYXNzZXNbY2FyZC5jb2xvcl19YH0+XG4gICAgICAgICAgICAgIDxjYXJkLmljb24gY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJDcHVDaGlwSWNvbiIsIkNoZWNrQ2lyY2xlSWNvbiIsIkJvbHRJY29uIiwiU3RhdHNDYXJkcyIsInN0YXRzIiwiaXNMb2FkaW5nIiwiY2FyZHMiLCJuYW1lIiwidmFsdWUiLCJhY3RpdmVDcmV3cyIsImljb24iLCJjb2xvciIsImNoYW5nZSIsImNoYW5nZVR5cGUiLCJ0b3RhbFRhc2tzIiwic3VjY2Vzc1JhdGUiLCJhdmVyYWdlU3BlZWQiLCJjb2xvckNsYXNzZXMiLCJlbWVyYWxkIiwiYmx1ZSIsImdyZWVuIiwicHVycGxlIiwiZGl2IiwiY2xhc3NOYW1lIiwiQXJyYXkiLCJtYXAiLCJfIiwiaSIsImNhcmQiLCJpbmRleCIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZGVsYXkiLCJwIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/stats-cards.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layouts/dashboard-layout.tsx":
/*!*************************************************!*\
  !*** ./components/layouts/dashboard-layout.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_navigation_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/navigation/sidebar */ \"(ssr)/./components/navigation/sidebar.tsx\");\n/* harmony import */ var _components_navigation_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/navigation/header */ \"(ssr)/./components/navigation/header.tsx\");\n/* harmony import */ var _components_navigation_mobile_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/navigation/mobile-menu */ \"(ssr)/./components/navigation/mobile-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_mobile_menu__WEBPACK_IMPORTED_MODULE_4__.MobileMenu, {\n                open: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        onMenuClick: ()=>setSidebarOpen(true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layouts/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/header.tsx":
/*!******************************************!*\
  !*** ./components/navigation/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header({ onMenuClick }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-4 border-b border-slate-200 bg-white/80 backdrop-blur-sm px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"-m-2.5 p-2.5 text-slate-700 lg:hidden\",\n                onClick: onMenuClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Open sidebar\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-6 w-6\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-6 w-px bg-slate-200 lg:hidden\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-slate-400 pl-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"search-field\",\n                                className: \"block h-full w-full border-0 py-0 pl-10 pr-0 text-slate-900 placeholder:text-slate-400 focus:ring-0 bg-transparent sm:text-sm\",\n                                placeholder: \"Search crews, templates, or agents...\",\n                                type: \"search\",\n                                name: \"search\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"relative -m-2.5 p-2.5 text-slate-400 hover:text-slate-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"View notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block lg:h-6 lg:w-px lg:bg-slate-200\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n                                as: \"div\",\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Button, {\n                                        className: \"-m-1.5 flex items-center p-1.5 hover:bg-slate-50 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open user menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-white\",\n                                                    children: \"U\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden lg:flex lg:items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-4 text-sm font-semibold leading-6 text-slate-900\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: \"User Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5 text-slate-400\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_7__.Transition, {\n                                        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                        enter: \"transition ease-out duration-100\",\n                                        enterFrom: \"transform opacity-0 scale-95\",\n                                        enterTo: \"transform opacity-100 scale-100\",\n                                        leave: \"transition ease-in duration-75\",\n                                        leaveFrom: \"transform opacity-100 scale-100\",\n                                        leaveTo: \"transform opacity-0 scale-95\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Items, {\n                                            className: \"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-slate-900/5 focus:outline-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: `block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? \"bg-slate-50\" : \"\"}`,\n                                                            children: \"Your profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: `block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? \"bg-slate-50\" : \"\"}`,\n                                                            children: \"Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: `block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? \"bg-slate-50\" : \"\"}`,\n                                                            children: \"Sign out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/mobile-menu.tsx":
/*!***********************************************!*\
  !*** ./components/navigation/mobile-menu.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileMenu: () => (/* binding */ MobileMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/navigation/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ MobileMenu auto */ \n\n\n\n\nfunction MobileMenu({ open, onClose }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Root, {\n        show: open,\n        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n            as: \"div\",\n            className: \"relative z-50 lg:hidden\",\n            onClose: onClose,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Child, {\n                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                    enter: \"transition-opacity ease-linear duration-300\",\n                    enterFrom: \"opacity-0\",\n                    enterTo: \"opacity-100\",\n                    leave: \"transition-opacity ease-linear duration-300\",\n                    leaveFrom: \"opacity-100\",\n                    leaveTo: \"opacity-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-slate-900/80\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Child, {\n                        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                        enter: \"transition ease-in-out duration-300 transform\",\n                        enterFrom: \"-translate-x-full\",\n                        enterTo: \"translate-x-0\",\n                        leave: \"transition ease-in-out duration-300 transform\",\n                        leaveFrom: \"translate-x-0\",\n                        leaveTo: \"-translate-x-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Dialog.Panel, {\n                            className: \"relative mr-16 flex w-full max-w-xs flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-in-out duration-300\",\n                                    enterFrom: \"opacity-0\",\n                                    enterTo: \"opacity-100\",\n                                    leave: \"ease-in-out duration-300\",\n                                    leaveFrom: \"opacity-100\",\n                                    leaveTo: \"opacity-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-0 flex w-16 justify-center pt-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"-m-2.5 p-2.5\",\n                                            onClick: onClose,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Close sidebar\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\",\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/mobile-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/sidebar.tsx":
/*!*******************************************!*\
  !*** ./components/navigation/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"My Crews\",\n        href: \"/crews\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Templates\",\n        href: \"/templates\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-slate-900 px-6 pb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-20 shrink-0 items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"CrewCraft\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block text-xs text-slate-400\",\n                                    children: \"AI Platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                whileHover: {\n                    scale: 1.02\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/crews/new\",\n                    className: \"group flex w-full items-center justify-center gap-2 rounded-lg bg-primary-600 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        \"Create New Crew\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex flex-1 flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    role: \"list\",\n                    className: \"flex flex-1 flex-col gap-y-7\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-semibold leading-6 text-slate-400 uppercase tracking-wider\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"-mx-2 mt-4 space-y-1\",\n                                    children: navigation.map((item)=>{\n                                        const isActive = pathname === item.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(isActive ? \"bg-slate-800 text-white\" : \"text-slate-400 hover:text-white hover:bg-slate-800\", \"group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium transition-colors\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(isActive ? \"text-white\" : \"text-slate-400 group-hover:text-white\", \"h-5 w-5 shrink-0\"),\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.name,\n                                                    isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                        layoutId: \"activeIndicator\",\n                                                        className: \"ml-auto h-2 w-2 rounded-full bg-primary-500\",\n                                                        transition: {\n                                                            type: \"spring\",\n                                                            duration: 0.3\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"mt-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg bg-slate-800 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: \"U\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-400\",\n                                                    children: \"Pro Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAppContext: () => (/* binding */ useAppContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAppContext,Providers auto */ \n\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAppContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (context === undefined) {\n        throw new Error(\"useAppContext must be used within a Providers component\");\n    }\n    return context;\n}\nfunction Providers({ children }) {\n    const value = {\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0RDtBQVU1RCxNQUFNRSwyQkFBYUYsb0RBQWFBLENBQTZCRztBQUV0RCxTQUFTQztJQUNkLE1BQU1DLFVBQVVKLGlEQUFVQSxDQUFDQztJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1Q7QUFFTyxTQUFTRSxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQsTUFBTUMsUUFBd0I7SUFFOUI7SUFFQSxxQkFDRSw4REFBQ1AsV0FBV1EsUUFBUTtRQUFDRCxPQUFPQTtrQkFDekJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5pbnRlcmZhY2UgQXBwQ29udGV4dFR5cGUge1xuICAvLyBBZGQgYW55IGdsb2JhbCBzdGF0ZSBvciBmdW5jdGlvbnMgaGVyZVxufVxuXG5jb25zdCBBcHBDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBcHBDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBwQ29udGV4dCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXBwQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXBwQ29udGV4dCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgUHJvdmlkZXJzIGNvbXBvbmVudCcpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIGNvbnN0IHZhbHVlOiBBcHBDb250ZXh0VHlwZSA9IHtcbiAgICAvLyBJbml0aWFsaXplIGNvbnRleHQgdmFsdWVzIGhlcmVcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEFwcENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0FwcENvbnRleHQuUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsIkFwcENvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VBcHBDb250ZXh0IiwiY29udGV4dCIsIkVycm9yIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/use-active-crews.ts":
/*!***************************************!*\
  !*** ./lib/hooks/use-active-crews.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useActiveCrews: () => (/* binding */ useActiveCrews)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n\n\nfunction useActiveCrews() {\n    const [crews, setCrews] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchActiveCrews();\n    }, []);\n    async function fetchActiveCrews() {\n        try {\n            setIsLoading(true);\n            // If no Supabase credentials, use mock data immediately\n            if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.hasSupabaseCredentials)()) {\n                console.log(\"Using mock data - Supabase credentials not configured\");\n                setMockData();\n                return;\n            }\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"crews\").select(`\n          *,\n          agents (*),\n          tasks (*)\n        `).in(\"status\", [\n                \"running\",\n                \"pending\"\n            ]).order(\"updated_at\", {\n                ascending: false\n            }).limit(10);\n            if (error) throw error;\n            setCrews(data || []);\n        } catch (error) {\n            console.error(\"Error fetching active crews:\", error);\n            setMockData();\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    function setMockData() {\n        // Mock data for demo\n        setCrews([\n            {\n                id: \"1\",\n                name: \"Research Crew\",\n                description: \"AI research and analysis team\",\n                status: \"running\",\n                progress: 75,\n                model: \"Cerebras Llama3.1-70B\",\n                agents: [\n                    {\n                        id: \"1\",\n                        role: \"Researcher\",\n                        goal: \"Research topics\",\n                        backstory: \"Expert researcher\",\n                        tools: [\n                            \"web_search\"\n                        ]\n                    },\n                    {\n                        id: \"2\",\n                        role: \"Writer\",\n                        goal: \"Write reports\",\n                        backstory: \"Professional writer\",\n                        tools: [\n                            \"text_generation\"\n                        ]\n                    },\n                    {\n                        id: \"3\",\n                        role: \"Reviewer\",\n                        goal: \"Review content\",\n                        backstory: \"Quality reviewer\",\n                        tools: [\n                            \"analysis\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"1\",\n                        description: \"Research market trends\",\n                        expected_output: \"Market analysis report\",\n                        status: \"completed\"\n                    },\n                    {\n                        id: \"2\",\n                        description: \"Write summary\",\n                        expected_output: \"Executive summary\",\n                        status: \"running\"\n                    }\n                ],\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"2\",\n                name: \"Content Pipeline\",\n                description: \"Content creation and optimization\",\n                status: \"pending\",\n                progress: 25,\n                model: \"Cerebras Llama3.1-8B\",\n                agents: [\n                    {\n                        id: \"4\",\n                        role: \"Content Creator\",\n                        goal: \"Create content\",\n                        backstory: \"Creative writer\",\n                        tools: [\n                            \"text_generation\"\n                        ]\n                    },\n                    {\n                        id: \"5\",\n                        role: \"SEO Optimizer\",\n                        goal: \"Optimize for SEO\",\n                        backstory: \"SEO expert\",\n                        tools: [\n                            \"seo_analysis\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"3\",\n                        description: \"Generate blog posts\",\n                        expected_output: \"Blog content\",\n                        status: \"pending\"\n                    }\n                ],\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"3\",\n                name: \"Data Analysis\",\n                description: \"Data processing and insights\",\n                status: \"completed\",\n                progress: 100,\n                model: \"Cerebras Llama3.1-70B\",\n                agents: [\n                    {\n                        id: \"6\",\n                        role: \"Data Analyst\",\n                        goal: \"Analyze data\",\n                        backstory: \"Data expert\",\n                        tools: [\n                            \"data_analysis\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"4\",\n                        description: \"Process dataset\",\n                        expected_output: \"Analysis report\",\n                        status: \"completed\"\n                    }\n                ],\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }\n        ]);\n    }\n    return {\n        crews,\n        isLoading,\n        refetch: fetchActiveCrews\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/use-active-crews.ts\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/use-activity-feed.ts":
/*!****************************************!*\
  !*** ./lib/hooks/use-activity-feed.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useActivityFeed: () => (/* binding */ useActivityFeed)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n\n\nfunction useActivityFeed() {\n    const [activities, setActivities] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchActivities();\n    }, []);\n    async function fetchActivities() {\n        try {\n            setIsLoading(true);\n            // If no Supabase credentials, use mock data immediately\n            if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.hasSupabaseCredentials)()) {\n                console.log(\"Using mock activities - Supabase credentials not configured\");\n                setMockActivities();\n                return;\n            }\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"activities\").select(\"*\").order(\"timestamp\", {\n                ascending: false\n            }).limit(10);\n            if (error) throw error;\n            setActivities(data || []);\n        } catch (error) {\n            console.error(\"Error fetching activities:\", error);\n            setMockActivities();\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    function setMockActivities() {\n        // Mock data for demo\n        setActivities([\n            {\n                id: \"1\",\n                type: \"success\",\n                title: \"Crew Completed\",\n                description: \"Research Crew finished market analysis task\",\n                crewName: \"Research Crew\",\n                timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString()\n            },\n            {\n                id: \"2\",\n                type: \"info\",\n                title: \"New Task Started\",\n                description: \"Content Pipeline began blog post generation\",\n                crewName: \"Content Pipeline\",\n                timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString()\n            },\n            {\n                id: \"3\",\n                type: \"warning\",\n                title: \"Rate Limit Warning\",\n                description: \"API usage approaching daily limit\",\n                timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()\n            },\n            {\n                id: \"4\",\n                type: \"success\",\n                title: \"Template Created\",\n                description: 'New workflow template \"Data Analysis\" created',\n                timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString()\n            },\n            {\n                id: \"5\",\n                type: \"info\",\n                title: \"Crew Updated\",\n                description: \"Research Crew configuration updated\",\n                crewName: \"Research Crew\",\n                timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString()\n            }\n        ]);\n    }\n    return {\n        activities,\n        isLoading,\n        refetch: fetchActivities\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaG9va3MvdXNlLWFjdGl2aXR5LWZlZWQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEyQztBQUVzQjtBQUUxRCxTQUFTSTtJQUNkLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHTiwrQ0FBUUEsQ0FBYSxFQUFFO0lBQzNELE1BQU0sQ0FBQ08sV0FBV0MsYUFBYSxHQUFHUiwrQ0FBUUEsQ0FBQztJQUUzQ0MsZ0RBQVNBLENBQUM7UUFDUlE7SUFDRixHQUFHLEVBQUU7SUFFTCxlQUFlQTtRQUNiLElBQUk7WUFDRkQsYUFBYTtZQUViLHdEQUF3RDtZQUN4RCxJQUFJLENBQUNMLHFFQUFzQkEsSUFBSTtnQkFDN0JPLFFBQVFDLEdBQUcsQ0FBQztnQkFDWkM7Z0JBQ0E7WUFDRjtZQUVBLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNWixtREFBUUEsQ0FDbkNhLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUMsS0FDUEMsS0FBSyxDQUFDLGFBQWE7Z0JBQUVDLFdBQVc7WUFBTSxHQUN0Q0MsS0FBSyxDQUFDO1lBRVQsSUFBSUwsT0FBTyxNQUFNQTtZQUVqQlIsY0FBY08sUUFBUSxFQUFFO1FBQzFCLEVBQUUsT0FBT0MsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsOEJBQThCQTtZQUM1Q0Y7UUFDRixTQUFVO1lBQ1JKLGFBQWE7UUFDZjtJQUNGO0lBRUEsU0FBU0k7UUFDTCxxQkFBcUI7UUFDckJOLGNBQWM7WUFDWjtnQkFDRWMsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsVUFBVTtnQkFDVkMsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssT0FBTyxLQUFLLEdBQUdDLFdBQVc7WUFDN0Q7WUFDQTtnQkFDRVIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsVUFBVTtnQkFDVkMsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssT0FBTyxLQUFLLElBQUlDLFdBQVc7WUFDOUQ7WUFDQTtnQkFDRVIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkUsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssT0FBTyxLQUFLLElBQUlDLFdBQVc7WUFDOUQ7WUFDQTtnQkFDRVIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkUsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssT0FBTyxLQUFLLElBQUlDLFdBQVc7WUFDOUQ7WUFDQTtnQkFDRVIsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsVUFBVTtnQkFDVkMsV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssT0FBTyxLQUFLLEtBQUtDLFdBQVc7WUFDL0Q7U0FDRDtJQUNMO0lBRUEsT0FBTztRQUFFdkI7UUFBWUU7UUFBV3NCLFNBQVNwQjtJQUFnQjtBQUMzRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2xpYi9ob29rcy91c2UtYWN0aXZpdHktZmVlZC50cz80YmJmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEFjdGl2aXR5IH0gZnJvbSAnQC90eXBlcy9hY3Rpdml0eSdcbmltcG9ydCB7IHN1cGFiYXNlLCBoYXNTdXBhYmFzZUNyZWRlbnRpYWxzIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBY3Rpdml0eUZlZWQoKSB7XG4gIGNvbnN0IFthY3Rpdml0aWVzLCBzZXRBY3Rpdml0aWVzXSA9IHVzZVN0YXRlPEFjdGl2aXR5W10+KFtdKVxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoQWN0aXZpdGllcygpXG4gIH0sIFtdKVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGZldGNoQWN0aXZpdGllcygpIHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpXG5cbiAgICAgIC8vIElmIG5vIFN1cGFiYXNlIGNyZWRlbnRpYWxzLCB1c2UgbW9jayBkYXRhIGltbWVkaWF0ZWx5XG4gICAgICBpZiAoIWhhc1N1cGFiYXNlQ3JlZGVudGlhbHMoKSkge1xuICAgICAgICBjb25zb2xlLmxvZygnVXNpbmcgbW9jayBhY3Rpdml0aWVzIC0gU3VwYWJhc2UgY3JlZGVudGlhbHMgbm90IGNvbmZpZ3VyZWQnKVxuICAgICAgICBzZXRNb2NrQWN0aXZpdGllcygpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnYWN0aXZpdGllcycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAub3JkZXIoJ3RpbWVzdGFtcCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAubGltaXQoMTApXG5cbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcblxuICAgICAgc2V0QWN0aXZpdGllcyhkYXRhIHx8IFtdKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBhY3Rpdml0aWVzOicsIGVycm9yKVxuICAgICAgc2V0TW9ja0FjdGl2aXRpZXMoKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgZnVuY3Rpb24gc2V0TW9ja0FjdGl2aXRpZXMoKSB7XG4gICAgICAvLyBNb2NrIGRhdGEgZm9yIGRlbW9cbiAgICAgIHNldEFjdGl2aXRpZXMoW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsXG4gICAgICAgICAgdGl0bGU6ICdDcmV3IENvbXBsZXRlZCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdSZXNlYXJjaCBDcmV3IGZpbmlzaGVkIG1hcmtldCBhbmFseXNpcyB0YXNrJyxcbiAgICAgICAgICBjcmV3TmFtZTogJ1Jlc2VhcmNoIENyZXcnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEwMDAgKiA2MCAqIDUpLnRvSVNPU3RyaW5nKCksIC8vIDUgbWludXRlcyBhZ29cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMicsXG4gICAgICAgICAgdHlwZTogJ2luZm8nLFxuICAgICAgICAgIHRpdGxlOiAnTmV3IFRhc2sgU3RhcnRlZCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdDb250ZW50IFBpcGVsaW5lIGJlZ2FuIGJsb2cgcG9zdCBnZW5lcmF0aW9uJyxcbiAgICAgICAgICBjcmV3TmFtZTogJ0NvbnRlbnQgUGlwZWxpbmUnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEwMDAgKiA2MCAqIDE1KS50b0lTT1N0cmluZygpLCAvLyAxNSBtaW51dGVzIGFnb1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgICB0eXBlOiAnd2FybmluZycsXG4gICAgICAgICAgdGl0bGU6ICdSYXRlIExpbWl0IFdhcm5pbmcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQVBJIHVzYWdlIGFwcHJvYWNoaW5nIGRhaWx5IGxpbWl0JyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAxMDAwICogNjAgKiAzMCkudG9JU09TdHJpbmcoKSwgLy8gMzAgbWludXRlcyBhZ29cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnNCcsXG4gICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICAgIHRpdGxlOiAnVGVtcGxhdGUgQ3JlYXRlZCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdOZXcgd29ya2Zsb3cgdGVtcGxhdGUgXCJEYXRhIEFuYWx5c2lzXCIgY3JlYXRlZCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMTAwMCAqIDYwICogNjApLnRvSVNPU3RyaW5nKCksIC8vIDEgaG91ciBhZ29cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnNScsXG4gICAgICAgICAgdHlwZTogJ2luZm8nLFxuICAgICAgICAgIHRpdGxlOiAnQ3JldyBVcGRhdGVkJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1Jlc2VhcmNoIENyZXcgY29uZmlndXJhdGlvbiB1cGRhdGVkJyxcbiAgICAgICAgICBjcmV3TmFtZTogJ1Jlc2VhcmNoIENyZXcnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEwMDAgKiA2MCAqIDEyMCkudG9JU09TdHJpbmcoKSwgLy8gMiBob3VycyBhZ29cbiAgICAgICAgfSxcbiAgICAgIF0pXG4gIH1cblxuICByZXR1cm4geyBhY3Rpdml0aWVzLCBpc0xvYWRpbmcsIHJlZmV0Y2g6IGZldGNoQWN0aXZpdGllcyB9XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJzdXBhYmFzZSIsImhhc1N1cGFiYXNlQ3JlZGVudGlhbHMiLCJ1c2VBY3Rpdml0eUZlZWQiLCJhY3Rpdml0aWVzIiwic2V0QWN0aXZpdGllcyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImZldGNoQWN0aXZpdGllcyIsImNvbnNvbGUiLCJsb2ciLCJzZXRNb2NrQWN0aXZpdGllcyIsImRhdGEiLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJvcmRlciIsImFzY2VuZGluZyIsImxpbWl0IiwiaWQiLCJ0eXBlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImNyZXdOYW1lIiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInRvSVNPU3RyaW5nIiwicmVmZXRjaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/use-activity-feed.ts\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/use-dashboard-stats.ts":
/*!******************************************!*\
  !*** ./lib/hooks/use-dashboard-stats.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDashboardStats: () => (/* binding */ useDashboardStats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n\n\nfunction useDashboardStats() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchStats();\n    }, []);\n    async function fetchStats() {\n        try {\n            setIsLoading(true);\n            // If no Supabase credentials, use mock data\n            if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.hasSupabaseCredentials)()) {\n                console.log(\"Using mock stats - Supabase credentials not configured\");\n                setStats({\n                    activeCrews: 3,\n                    totalTasks: 12,\n                    successRate: 94,\n                    averageSpeed: 2.3\n                });\n                return;\n            }\n            // Fetch active crews count\n            const { count: activeCrews } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"crews\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"running\");\n            // Fetch total tasks count\n            const { count: totalTasks } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"tasks\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Fetch completed tasks for success rate\n            const { count: completedTasks } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"tasks\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"completed\");\n            // Calculate success rate\n            const successRate = totalTasks ? Math.round(completedTasks / totalTasks * 100) : 0;\n            // Mock average speed (would come from performance metrics in real app)\n            const averageSpeed = 2.3;\n            setStats({\n                activeCrews: activeCrews || 0,\n                totalTasks: totalTasks || 0,\n                successRate,\n                averageSpeed\n            });\n        } catch (error) {\n            console.error(\"Error fetching dashboard stats:\", error);\n            // Set default stats on error\n            setStats({\n                activeCrews: 12,\n                totalTasks: 147,\n                successRate: 94,\n                averageSpeed: 2.3\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    return {\n        stats,\n        isLoading,\n        refetch: fetchStats\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/use-dashboard-stats.ts\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/use-live-preview.ts":
/*!***************************************!*\
  !*** ./lib/hooks/use-live-preview.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLivePreview: () => (/* binding */ useLivePreview)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useLivePreview() {\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [currentCrew, setCurrentCrew] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const addLog = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((log)=>{\n        setLogs((prev)=>[\n                ...prev,\n                log\n            ].slice(-100)) // Keep only last 100 logs\n        ;\n    }, []);\n    const startPreview = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setIsRunning(true);\n        setCurrentCrew(\"Research Crew\");\n        setLogs([]);\n        // Simulate live logs\n        const mockLogs = [\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"info\",\n                agent: \"Researcher\",\n                message: \"Starting market trend analysis...\"\n            },\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"info\",\n                agent: \"Researcher\",\n                message: \"Searching for latest AI market data...\"\n            },\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"success\",\n                agent: \"Researcher\",\n                message: \"Found 47 relevant sources for analysis\"\n            },\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"info\",\n                agent: \"Writer\",\n                message: \"Generating executive summary...\"\n            },\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"info\",\n                agent: \"Writer\",\n                message: '{\"section\": \"market_overview\", \"progress\": 45, \"words\": 287}'\n            },\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"info\",\n                agent: \"Reviewer\",\n                message: \"Validating content quality and accuracy...\"\n            },\n            {\n                timestamp: new Date().toLocaleTimeString(),\n                level: \"success\",\n                agent: \"Reviewer\",\n                message: \"Content quality score: 94/100\"\n            }\n        ];\n        let index = 0;\n        const interval = setInterval(()=>{\n            if (index < mockLogs.length) {\n                addLog(mockLogs[index]);\n                index++;\n            } else {\n                clearInterval(interval);\n                setIsRunning(false);\n                addLog({\n                    timestamp: new Date().toLocaleTimeString(),\n                    level: \"success\",\n                    agent: \"System\",\n                    message: \"Task completed successfully!\"\n                });\n            }\n        }, 2000);\n        return ()=>clearInterval(interval);\n    }, [\n        addLog\n    ]);\n    const stopPreview = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setIsRunning(false);\n        addLog({\n            timestamp: new Date().toLocaleTimeString(),\n            level: \"warning\",\n            agent: \"System\",\n            message: \"Preview stopped by user\"\n        });\n    }, [\n        addLog\n    ]);\n    return {\n        logs,\n        isRunning,\n        currentCrew,\n        startPreview,\n        stopPreview\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/use-live-preview.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasSupabaseCredentials: () => (/* binding */ hasSupabaseCredentials),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Use fallback values for development when env vars are not set\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || \"https://placeholder.supabase.co\";\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || \"placeholder-key\";\n// Create a mock client if no real credentials are provided\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Helper to check if we have real Supabase credentials\nconst hasSupabaseCredentials = ()=>{\n    return process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY && process.env.NEXT_PUBLIC_SUPABASE_URL !== \"https://placeholder.supabase.co\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"33e1a610ba87\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9hcHAvZ2xvYmFscy5jc3M/NjgyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzZTFhNjEwYmE4N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"CrewCraft AI Platform - Multi-Agent AI Orchestration\",\n    description: \"Create, manage, and deploy AI agent crews with Cerebras ultra-fast inference. Build sophisticated multi-agent workflows for complex tasks.\",\n    keywords: \"AI, CrewAI, Cerebras, Multi-Agent, Artificial Intelligence, Automation, Workflows\",\n    authors: [\n        {\n            name: \"CrewCraft Team\"\n        }\n    ],\n    openGraph: {\n        title: \"CrewCraft AI Platform\",\n        description: \"Enterprise-grade multi-agent AI orchestration platform\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-slate-50 text-slate-800\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            className: \"bg-white shadow-lg border border-slate-200\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e1),
/* harmony export */   useAppContext: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#useAppContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/framer-motion","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/@heroicons","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx","vendor-chunks/@headlessui","vendor-chunks/@babel","vendor-chunks/refractor","vendor-chunks/react-syntax-highlighter","vendor-chunks/date-fns","vendor-chunks/property-information","vendor-chunks/hastscript","vendor-chunks/parse-entities","vendor-chunks/character-reference-invalid","vendor-chunks/character-entities","vendor-chunks/character-entities-legacy","vendor-chunks/xtend","vendor-chunks/space-separated-tokens","vendor-chunks/is-hexadecimal","vendor-chunks/is-decimal","vendor-chunks/is-alphanumerical","vendor-chunks/is-alphabetical","vendor-chunks/hast-util-parse-selector","vendor-chunks/comma-separated-tokens"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();