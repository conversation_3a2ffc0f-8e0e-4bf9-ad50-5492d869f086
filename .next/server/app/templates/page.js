/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/templates/page";
exports.ids = ["app/templates/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'templates',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/templates/page.tsx */ \"(rsc)/./app/templates/page.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/templates/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/templates/page\",\n        pathname: \"/templates\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Ftemplates%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Ftemplates%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/templates/page.tsx */ \"(ssr)/./app/templates/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZhcHAlMkZ0ZW1wbGF0ZXMlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vP2Y5YTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2FwcC90ZW1wbGF0ZXMvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Ftemplates%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGcmFqc2hhaCUyRkRvd25sb2FkcyUyRlByb2plY3RzJTJGQUlDcmV3RGVja2VyJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySmV0QnJhaW5zX01vbm8lMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1qZXRicmFpbnMtbW9ubyUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmpldGJyYWluc01vbm8lMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBNEc7QUFDNUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vP2MxYTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhanNoYWgvRG93bmxvYWRzL1Byb2plY3RzL0FJQ3Jld0RlY2tlci9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/templates/page.tsx":
/*!********************************!*\
  !*** ./app/templates/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplatesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_templates_template_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/templates/template-card */ \"(ssr)/./components/templates/template-card.tsx\");\n/* harmony import */ var _components_templates_template_filters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/templates/template-filters */ \"(ssr)/./components/templates/template-filters.tsx\");\n/* harmony import */ var _components_templates_template_categories__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/templates/template-categories */ \"(ssr)/./components/templates/template-categories.tsx\");\n/* harmony import */ var _lib_hooks_use_templates__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/hooks/use-templates */ \"(ssr)/./lib/hooks/use-templates.ts\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _components_layouts_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layouts/dashboard-layout */ \"(ssr)/./components/layouts/dashboard-layout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction TemplatesPage() {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedDifficulty, setSelectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"popular\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { templates, categories, isLoading } = (0,_lib_hooks_use_templates__WEBPACK_IMPORTED_MODULE_5__.useTemplates)();\n    const filteredTemplates = templates.filter((template)=>{\n        const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) || template.description.toLowerCase().includes(searchQuery.toLowerCase()) || template.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || template.category === selectedCategory;\n        const matchesDifficulty = selectedDifficulty === \"all\" || template.difficulty === selectedDifficulty;\n        return matchesSearch && matchesCategory && matchesDifficulty;\n    });\n    const sortedTemplates = [\n        ...filteredTemplates\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case \"popular\":\n                return b.usageCount - a.usageCount;\n            case \"rating\":\n                return b.rating - a.rating;\n            case \"newest\":\n                return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n            case \"name\":\n                return a.name.localeCompare(b.name);\n            default:\n                return 0;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary-100 to-primary-300 rounded-full text-primary-700 text-sm font-medium mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                \"Template Gallery\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-slate-900 mb-4\",\n                            children: \"Ready-to-Use AI Crew Templates\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-600 max-w-3xl mx-auto\",\n                            children: \"Jumpstart your AI projects with professionally crafted crew templates. From research and content creation to customer support and analytics.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_templates_template_categories__WEBPACK_IMPORTED_MODULE_4__.TemplateCategories, {\n                        categories: categories,\n                        selectedCategory: selectedCategory,\n                        onCategorySelect: setSelectedCategory\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 15\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.2\n                    },\n                    className: \"bg-white rounded-xl border border-slate-200 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search templates by name, description, or tags...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: sortBy,\n                                    onChange: (e)=>setSortBy(e.target.value),\n                                    className: \"px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"popular\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rating\",\n                                            children: \"Highest Rated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"newest\",\n                                            children: \"Newest\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"name\",\n                                            children: \"Name A-Z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedDifficulty,\n                                    onChange: (e)=>setSelectedDifficulty(e.target.value),\n                                    className: \"px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"All Levels\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"beginner\",\n                                            children: \"Beginner\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"intermediate\",\n                                            children: \"Intermediate\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"advanced\",\n                                            children: \"Advanced\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center bg-slate-100 rounded-lg p-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: `p-2 rounded-md transition-colors ${viewMode === \"grid\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-slate-600 hover:text-slate-900\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"list\"),\n                                            className: `p-2 rounded-md transition-colors ${viewMode === \"list\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-slate-600 hover:text-slate-900\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"inline-flex items-center gap-2 px-4 py-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 17\n                        }, this),\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: \"auto\"\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"mt-6 pt-6 border-t border-slate-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_templates_template_filters__WEBPACK_IMPORTED_MODULE_3__.TemplateFilters, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 19\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 15\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: [\n                                        \"Showing \",\n                                        sortedTemplates.length,\n                                        \" of \",\n                                        templates.length,\n                                        \" templates\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 19\n                                }, this),\n                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSearchQuery(\"\"),\n                                    className: \"text-sm text-primary-600 hover:text-primary-700\",\n                                    children: \"Clear search\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm text-amber-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Trending templates\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 15\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.4\n                    },\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `grid gap-6 ${viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 xl:grid-cols-3\" : \"grid-cols-1\"}`,\n                        children: [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl border border-slate-200 p-6 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-3/4 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-slate-200 rounded w-full mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-slate-200 rounded w-2/3 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 bg-slate-200 rounded w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 27\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 bg-slate-200 rounded w-20\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 23\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 19\n                    }, this) : sortedTemplates.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `grid gap-6 ${viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 xl:grid-cols-3\" : \"grid-cols-1\"}`,\n                        children: sortedTemplates.map((template, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.95\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    delay: index * 0.05\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_templates_template_card__WEBPACK_IMPORTED_MODULE_2__.TemplateCard, {\n                                    template: template,\n                                    viewMode: viewMode\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 25\n                                }, this)\n                            }, template.id, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 23\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 19\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-24 w-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 text-slate-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 23\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-slate-900 mb-2\",\n                                children: \"No templates found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 mb-6\",\n                                children: \"Try adjusting your search or filters to find what you're looking for\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setSelectedCategory(\"all\");\n                                    setSelectedDifficulty(\"all\");\n                                },\n                                className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                                children: \"Clear all filters\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 19\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 15\n                }, this),\n                sortedTemplates.length > 0 && sortedTemplates.length < templates.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.5\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-8 py-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors\",\n                        children: \"Load More Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/templates/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layouts/dashboard-layout.tsx":
/*!*************************************************!*\
  !*** ./components/layouts/dashboard-layout.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_navigation_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/navigation/sidebar */ \"(ssr)/./components/navigation/sidebar.tsx\");\n/* harmony import */ var _components_navigation_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/navigation/header */ \"(ssr)/./components/navigation/header.tsx\");\n/* harmony import */ var _components_navigation_mobile_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/navigation/mobile-menu */ \"(ssr)/./components/navigation/mobile-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_mobile_menu__WEBPACK_IMPORTED_MODULE_4__.MobileMenu, {\n                open: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        onMenuClick: ()=>setSidebarOpen(true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layouts/dashboard-layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layouts/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/header.tsx":
/*!******************************************!*\
  !*** ./components/navigation/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header({ onMenuClick }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-4 border-b border-slate-200 bg-white/80 backdrop-blur-sm px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"-m-2.5 p-2.5 text-slate-700 lg:hidden\",\n                onClick: onMenuClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: \"Open sidebar\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-6 w-6\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-6 w-px bg-slate-200 lg:hidden\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex flex-1 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-slate-400 pl-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"search-field\",\n                                className: \"block h-full w-full border-0 py-0 pl-10 pr-0 text-slate-900 placeholder:text-slate-400 focus:ring-0 bg-transparent sm:text-sm\",\n                                placeholder: \"Search crews, templates, or agents...\",\n                                type: \"search\",\n                                name: \"search\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"relative -m-2.5 p-2.5 text-slate-400 hover:text-slate-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"View notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block lg:h-6 lg:w-px lg:bg-slate-200\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n                                as: \"div\",\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Button, {\n                                        className: \"-m-1.5 flex items-center p-1.5 hover:bg-slate-50 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open user menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-white\",\n                                                    children: \"U\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden lg:flex lg:items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-4 text-sm font-semibold leading-6 text-slate-900\",\n                                                        \"aria-hidden\": \"true\",\n                                                        children: \"User Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"ml-2 h-5 w-5 text-slate-400\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_7__.Transition, {\n                                        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                        enter: \"transition ease-out duration-100\",\n                                        enterFrom: \"transform opacity-0 scale-95\",\n                                        enterTo: \"transform opacity-100 scale-100\",\n                                        leave: \"transition ease-in duration-75\",\n                                        leaveFrom: \"transform opacity-100 scale-100\",\n                                        leaveTo: \"transform opacity-0 scale-95\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Items, {\n                                            className: \"absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-slate-900/5 focus:outline-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: `block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? \"bg-slate-50\" : \"\"}`,\n                                                            children: \"Your profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: `block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? \"bg-slate-50\" : \"\"}`,\n                                                            children: \"Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_5__.Menu.Item, {\n                                                    children: ({ active })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: `block px-3 py-1 text-sm leading-6 text-slate-900 ${active ? \"bg-slate-50\" : \"\"}`,\n                                                            children: \"Sign out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/header.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/mobile-menu.tsx":
/*!***********************************************!*\
  !*** ./components/navigation/mobile-menu.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileMenu: () => (/* binding */ MobileMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/navigation/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ MobileMenu auto */ \n\n\n\n\nfunction MobileMenu({ open, onClose }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Root, {\n        show: open,\n        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n            as: \"div\",\n            className: \"relative z-50 lg:hidden\",\n            onClose: onClose,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Child, {\n                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                    enter: \"transition-opacity ease-linear duration-300\",\n                    enterFrom: \"opacity-0\",\n                    enterTo: \"opacity-100\",\n                    leave: \"transition-opacity ease-linear duration-300\",\n                    leaveFrom: \"opacity-100\",\n                    leaveTo: \"opacity-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-slate-900/80\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Child, {\n                        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                        enter: \"transition ease-in-out duration-300 transform\",\n                        enterFrom: \"-translate-x-full\",\n                        enterTo: \"translate-x-0\",\n                        leave: \"transition ease-in-out duration-300 transform\",\n                        leaveFrom: \"translate-x-0\",\n                        leaveTo: \"-translate-x-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_4__.Dialog.Panel, {\n                            className: \"relative mr-16 flex w-full max-w-xs flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_3__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-in-out duration-300\",\n                                    enterFrom: \"opacity-0\",\n                                    enterTo: \"opacity-100\",\n                                    leave: \"ease-in-out duration-300\",\n                                    leaveFrom: \"opacity-100\",\n                                    leaveTo: \"opacity-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-0 flex w-16 justify-center pt-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"-m-2.5 p-2.5\",\n                                            onClick: onClose,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Close sidebar\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\",\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/mobile-menu.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/mobile-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/sidebar.tsx":
/*!*******************************************!*\
  !*** ./components/navigation/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,Cog6ToothIcon,CpuChipIcon,DocumentDuplicateIcon,HomeIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"My Crews\",\n        href: \"/crews\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Templates\",\n        href: \"/templates\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-slate-900 px-6 pb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-20 shrink-0 items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"CrewCraft\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block text-xs text-slate-400\",\n                                    children: \"AI Platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                whileHover: {\n                    scale: 1.02\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/crews/new\",\n                    className: \"group flex w-full items-center justify-center gap-2 rounded-lg bg-primary-600 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_Cog6ToothIcon_CpuChipIcon_DocumentDuplicateIcon_HomeIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        \"Create New Crew\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex flex-1 flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    role: \"list\",\n                    className: \"flex flex-1 flex-col gap-y-7\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs font-semibold leading-6 text-slate-400 uppercase tracking-wider\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"-mx-2 mt-4 space-y-1\",\n                                    children: navigation.map((item)=>{\n                                        const isActive = pathname === item.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(isActive ? \"bg-slate-800 text-white\" : \"text-slate-400 hover:text-white hover:bg-slate-800\", \"group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium transition-colors\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(isActive ? \"text-white\" : \"text-slate-400 group-hover:text-white\", \"h-5 w-5 shrink-0\"),\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.name,\n                                                    isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                        layoutId: \"activeIndicator\",\n                                                        className: \"ml-auto h-2 w-2 rounded-full bg-primary-500\",\n                                                        transition: {\n                                                            type: \"spring\",\n                                                            duration: 0.3\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"mt-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg bg-slate-800 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: \"U\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-400\",\n                                                    children: \"Pro Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/navigation/sidebar.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAppContext: () => (/* binding */ useAppContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAppContext,Providers auto */ \n\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAppContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (context === undefined) {\n        throw new Error(\"useAppContext must be used within a Providers component\");\n    }\n    return context;\n}\nfunction Providers({ children }) {\n    const value = {\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0RDtBQVU1RCxNQUFNRSwyQkFBYUYsb0RBQWFBLENBQTZCRztBQUV0RCxTQUFTQztJQUNkLE1BQU1DLFVBQVVKLGlEQUFVQSxDQUFDQztJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1Q7QUFFTyxTQUFTRSxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQsTUFBTUMsUUFBd0I7SUFFOUI7SUFFQSxxQkFDRSw4REFBQ1AsV0FBV1EsUUFBUTtRQUFDRCxPQUFPQTtrQkFDekJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5pbnRlcmZhY2UgQXBwQ29udGV4dFR5cGUge1xuICAvLyBBZGQgYW55IGdsb2JhbCBzdGF0ZSBvciBmdW5jdGlvbnMgaGVyZVxufVxuXG5jb25zdCBBcHBDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBcHBDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBwQ29udGV4dCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXBwQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXBwQ29udGV4dCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgUHJvdmlkZXJzIGNvbXBvbmVudCcpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIGNvbnN0IHZhbHVlOiBBcHBDb250ZXh0VHlwZSA9IHtcbiAgICAvLyBJbml0aWFsaXplIGNvbnRleHQgdmFsdWVzIGhlcmVcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEFwcENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0FwcENvbnRleHQuUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsIkFwcENvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VBcHBDb250ZXh0IiwiY29udGV4dCIsIkVycm9yIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/templates/template-card.tsx":
/*!************************************************!*\
  !*** ./components/templates/template-card.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCard: () => (/* binding */ TemplateCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckBadgeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BookmarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/BookmarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ TemplateCard auto */ \n\n\n\n\n\n\nfunction TemplateCard({ template, viewMode }) {\n    const [isBookmarked, setIsBookmarked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const difficultyColors = {\n        beginner: \"bg-green-100 text-green-700 border-green-200\",\n        intermediate: \"bg-yellow-100 text-yellow-700 border-yellow-200\",\n        advanced: \"bg-red-100 text-red-700 border-red-200\"\n    };\n    const categoryColors = {\n        research: \"bg-blue-100 text-blue-700\",\n        content: \"bg-purple-100 text-purple-700\",\n        support: \"bg-green-100 text-green-700\",\n        analytics: \"bg-orange-100 text-orange-700\",\n        sales: \"bg-pink-100 text-pink-700\",\n        marketing: \"bg-indigo-100 text-indigo-700\"\n    };\n    if (viewMode === \"list\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            whileHover: {\n                scale: 1.01\n            },\n            className: \"bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-all duration-200\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 w-24 h-24 bg-gradient-to-br from-primary-100 to-purple-100 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-10 w-10 text-primary-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-slate-900 truncate\",\n                                                        children: template.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    template.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm line-clamp-2\",\n                                                children: template.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsBookmarked(!isBookmarked),\n                                                className: \"p-2 text-slate-400 hover:text-primary-600 transition-colors\",\n                                                children: isBookmarked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsLiked(!isLiked),\n                                                className: \"p-2 text-slate-400 hover:text-red-500 transition-colors\",\n                                                children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/templates/${template.id}`,\n                                                className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Use Template\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 text-sm text-slate-500 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: template.rating\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    template.usageCount.toLocaleString(),\n                                                    \" uses\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: template.estimatedTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    template.agents.length,\n                                                    \" agents\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full border\", difficultyColors[template.difficulty]),\n                                        children: template.difficulty\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full\", categoryColors[template.category] || \"bg-slate-100 text-slate-700\"),\n                                        children: template.category\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    template.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs text-slate-600 bg-slate-100 rounded-full\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)),\n                                    template.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-slate-500\",\n                                        children: [\n                                            \"+\",\n                                            template.tags.length - 3,\n                                            \" more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        whileHover: {\n            scale: 1.02\n        },\n        className: \"bg-white rounded-xl border border-slate-200 overflow-hidden hover:shadow-lg transition-all duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-slate-900 line-clamp-1\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    template.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsBookmarked(!isBookmarked),\n                                        className: \"p-1 text-slate-400 hover:text-primary-600 transition-colors\",\n                                        children: isBookmarked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsLiked(!isLiked),\n                                        className: \"p-1 text-slate-400 hover:text-red-500 transition-colors\",\n                                        children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-600 text-sm line-clamp-3 mb-4\",\n                        children: template.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 text-sm text-slate-500 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: template.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: template.usageCount.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: template.estimatedTime\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-wrap mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full border\", difficultyColors[template.difficulty]),\n                                children: template.difficulty\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full\", categoryColors[template.category] || \"bg-slate-100 text-slate-700\"),\n                                children: template.category\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            template.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs text-slate-600 bg-slate-100 rounded-full\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)),\n                            template.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-slate-500\",\n                                children: [\n                                    \"+\",\n                                    template.tags.length - 2\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 bg-slate-50 border-t border-slate-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm text-slate-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        template.agents.length,\n                                        \" agents\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: `/templates/${template.id}`,\n                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Template\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/templates/template-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/templates/template-categories.tsx":
/*!******************************************************!*\
  !*** ./components/templates/template-categories.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCategories: () => (/* binding */ TemplateCategories)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MegaphoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ TemplateCategories auto */ \n\n\n\nconst categoryIcons = {\n    research: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    content: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    support: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    analytics: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    sales: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    marketing: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    automation: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    all: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\nconst categoryColors = {\n    research: \"from-blue-500 to-cyan-500\",\n    content: \"from-purple-500 to-pink-500\",\n    support: \"from-green-500 to-emerald-500\",\n    analytics: \"from-orange-500 to-red-500\",\n    sales: \"from-pink-500 to-rose-500\",\n    marketing: \"from-indigo-500 to-purple-500\",\n    automation: \"from-gray-500 to-slate-500\",\n    all: \"from-primary-500 to-primary-600\"\n};\nfunction TemplateCategories({ categories, selectedCategory, onCategorySelect }) {\n    // Add \"All\" category at the beginning\n    const allCategories = [\n        {\n            id: \"all\",\n            name: \"All Templates\",\n            description: \"Browse all available templates\",\n            icon: \"all\",\n            count: categories.reduce((sum, cat)=>sum + cat.count, 0)\n        },\n        ...categories\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4\",\n                children: allCategories.map((category, index)=>{\n                    const IconComponent = categoryIcons[category.id] || _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                    const isSelected = selectedCategory === category.id;\n                    const gradientClass = categoryColors[category.id] || categoryColors.all;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.05\n                        },\n                        onClick: ()=>onCategorySelect(category.id),\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"group relative p-4 rounded-xl border-2 transition-all duration-200 text-center\", isSelected ? \"border-primary-300 bg-primary-50 shadow-lg scale-105\" : \"border-slate-200 bg-white hover:border-slate-300 hover:shadow-md hover:scale-102\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"absolute inset-0 rounded-xl bg-gradient-to-br opacity-0 transition-opacity duration-200\", gradientClass, isSelected ? \"opacity-10\" : \"group-hover:opacity-5\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"relative w-12 h-12 mx-auto mb-3 rounded-lg flex items-center justify-center transition-all duration-200\", isSelected ? `bg-gradient-to-br ${gradientClass} text-white shadow-lg` : \"bg-slate-100 text-slate-600 group-hover:bg-slate-200\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"text-sm font-medium mb-1 transition-colors duration-200\", isSelected ? \"text-primary-900\" : \"text-slate-900 group-hover:text-slate-700\"),\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"text-xs transition-colors duration-200\", isSelected ? \"text-primary-600\" : \"text-slate-500 group-hover:text-slate-600\"),\n                                        children: [\n                                            category.count,\n                                            \" templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                layoutId: \"categorySelection\",\n                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center\",\n                                transition: {\n                                    type: \"spring\",\n                                    bounce: 0.2,\n                                    duration: 0.6\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            selectedCategory !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: \"auto\"\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                className: \"bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl p-6 border border-slate-200\",\n                children: (()=>{\n                    const category = categories.find((cat)=>cat.id === selectedCategory);\n                    if (!category) return null;\n                    const IconComponent = categoryIcons[category.id] || _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                    const gradientClass = categoryColors[category.id] || categoryColors.all;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"w-12 h-12 rounded-lg flex items-center justify-center bg-gradient-to-br text-white shadow-lg\", gradientClass),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-slate-900 mb-2\",\n                                        children: [\n                                            category.name,\n                                            \" Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600 mb-3\",\n                                        children: category.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-sm text-slate-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    category.count,\n                                                    \" templates available\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this),\n                                            category.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-700 rounded-full text-xs font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Featured Category\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 15\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: allCategories[0].count\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Total Templates\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: categories.filter((cat)=>cat.featured).length\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Featured\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: categories.length\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: \"4.8\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Avg Rating\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/templates/template-categories.tsx\n");

/***/ }),

/***/ "(ssr)/./components/templates/template-filters.tsx":
/*!***************************************************!*\
  !*** ./components/templates/template-filters.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateFilters: () => (/* binding */ TemplateFilters)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ TemplateFilters auto */ \n\n\n\n\n\nfunction TemplateFilters({ onFiltersChange }) {\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        minRating: 0,\n        maxEstimatedTime: 0,\n        tags: [],\n        agentCount: {\n            min: 0,\n            max: 10\n        },\n        featured: false,\n        difficulty: [],\n        sortBy: \"popular\"\n    });\n    const popularTags = [\n        \"research\",\n        \"content-creation\",\n        \"customer-support\",\n        \"data-analysis\",\n        \"marketing\",\n        \"sales\",\n        \"automation\",\n        \"reporting\",\n        \"social-media\",\n        \"email-marketing\",\n        \"lead-generation\",\n        \"competitive-analysis\"\n    ];\n    const difficulties = [\n        \"beginner\",\n        \"intermediate\",\n        \"advanced\"\n    ];\n    const estimatedTimes = [\n        {\n            label: \"Under 1 hour\",\n            value: 1\n        },\n        {\n            label: \"1-3 hours\",\n            value: 3\n        },\n        {\n            label: \"3-6 hours\",\n            value: 6\n        },\n        {\n            label: \"6+ hours\",\n            value: 24\n        }\n    ];\n    const updateFilters = (newFilters)=>{\n        const updatedFilters = {\n            ...filters,\n            ...newFilters\n        };\n        setFilters(updatedFilters);\n        onFiltersChange?.(updatedFilters);\n    };\n    const toggleTag = (tag)=>{\n        const newTags = filters.tags.includes(tag) ? filters.tags.filter((t)=>t !== tag) : [\n            ...filters.tags,\n            tag\n        ];\n        updateFilters({\n            tags: newTags\n        });\n    };\n    const toggleDifficulty = (difficulty)=>{\n        const newDifficulties = filters.difficulty.includes(difficulty) ? filters.difficulty.filter((d)=>d !== difficulty) : [\n            ...filters.difficulty,\n            difficulty\n        ];\n        updateFilters({\n            difficulty: newDifficulties\n        });\n    };\n    const clearAllFilters = ()=>{\n        const clearedFilters = {\n            minRating: 0,\n            maxEstimatedTime: 0,\n            tags: [],\n            agentCount: {\n                min: 0,\n                max: 10\n            },\n            featured: false,\n            difficulty: [],\n            sortBy: \"popular\"\n        };\n        setFilters(clearedFilters);\n        onFiltersChange?.(clearedFilters);\n    };\n    const hasActiveFilters = filters.minRating > 0 || filters.maxEstimatedTime > 0 || filters.tags.length > 0 || filters.difficulty.length > 0 || filters.featured || filters.agentCount.min > 0 || filters.agentCount.max < 10;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5 text-slate-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-slate-900\",\n                                children: \"Advanced Filters\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearAllFilters,\n                        className: \"inline-flex items-center gap-1 px-3 py-1 text-sm text-slate-600 hover:text-slate-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            \"Clear all\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-slate-700\",\n                                children: \"Minimum Rating\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    4,\n                                    3,\n                                    2,\n                                    1\n                                ].map((rating)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>updateFilters({\n                                                minRating: rating === filters.minRating ? 0 : rating\n                                            }),\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center gap-2 w-full p-2 rounded-lg border transition-colors\", filters.minRating === rating ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"h-4 w-4\", i < rating ? \"text-yellow-400 fill-current\" : \"text-slate-300\")\n                                                    }, i, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"& up\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, rating, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-slate-700\",\n                                children: \"Difficulty Level\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: difficulties.map((difficulty)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleDifficulty(difficulty),\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center justify-between w-full p-2 rounded-lg border transition-colors\", filters.difficulty.includes(difficulty) ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm capitalize\",\n                                                children: difficulty\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            filters.difficulty.includes(difficulty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, difficulty, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-slate-700\",\n                                children: \"Estimated Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: estimatedTimes.map((time)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>updateFilters({\n                                                maxEstimatedTime: time.value === filters.maxEstimatedTime ? 0 : time.value\n                                            }),\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center justify-between w-full p-2 rounded-lg border transition-colors\", filters.maxEstimatedTime === time.value ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: time.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            filters.maxEstimatedTime === time.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, time.value, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-slate-700\",\n                        children: \"Number of Agents\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 text-slate-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-slate-600\",\n                                        children: \"Min:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"0\",\n                                        max: \"10\",\n                                        value: filters.agentCount.min,\n                                        onChange: (e)=>updateFilters({\n                                                agentCount: {\n                                                    ...filters.agentCount,\n                                                    min: parseInt(e.target.value) || 0\n                                                }\n                                            }),\n                                        className: \"w-16 px-2 py-1 border border-slate-200 rounded text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-slate-600\",\n                                        children: \"Max:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"0\",\n                                        max: \"10\",\n                                        value: filters.agentCount.max,\n                                        onChange: (e)=>updateFilters({\n                                                agentCount: {\n                                                    ...filters.agentCount,\n                                                    max: parseInt(e.target.value) || 10\n                                                }\n                                            }),\n                                        className: \"w-16 px-2 py-1 border border-slate-200 rounded text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-slate-700\",\n                        children: \"Popular Tags\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: popularTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleTag(tag),\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium transition-colors\", filters.tags.includes(tag) ? \"bg-primary-100 text-primary-700 border border-primary-300\" : \"bg-slate-100 text-slate-700 border border-slate-200 hover:bg-slate-200\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    tag\n                                ]\n                            }, tag, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-slate-700\",\n                        children: \"Special Filters\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>updateFilters({\n                                    featured: !filters.featured\n                                }),\n                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center justify-between w-full p-3 rounded-lg border transition-colors\", filters.featured ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-amber-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Featured Templates Only\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                filters.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: \"auto\"\n                },\n                className: \"bg-primary-50 border border-primary-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-primary-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            filters.minRating > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                children: [\n                                    \"Rating \",\n                                    filters.minRating,\n                                    \"+ stars\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this),\n                            filters.maxEstimatedTime > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                children: [\n                                    \"Under \",\n                                    filters.maxEstimatedTime,\n                                    \"h\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this),\n                            filters.difficulty.map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                    children: diff\n                                }, diff, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this)),\n                            filters.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                    children: [\n                                        \"#\",\n                                        tag\n                                    ]\n                                }, tag, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)),\n                            filters.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                children: \"Featured\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/templates/template-filters.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/use-templates.ts":
/*!************************************!*\
  !*** ./lib/hooks/use-templates.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTemplates: () => (/* binding */ useTemplates)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useTemplates auto */ \n\nfunction useTemplates() {\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchTemplates();\n    }, []);\n    async function fetchTemplates() {\n        try {\n            setIsLoading(true);\n            setError(null);\n            // If no Supabase credentials, use mock data immediately\n            if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.hasSupabaseCredentials)()) {\n                console.log(\"Using mock templates data - Supabase credentials not configured\");\n                setMockData();\n                return;\n            }\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"templates\").select(\"*\").order(\"usage_count\", {\n                ascending: false\n            });\n            if (error) throw error;\n            setTemplates(data || []);\n        } catch (err) {\n            console.error(\"Error fetching templates:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch templates\");\n            setMockData();\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    function setMockData() {\n        const mockCategories = [\n            {\n                id: \"research\",\n                name: \"Research & Analysis\",\n                description: \"Data research, market analysis, and competitive intelligence\",\n                icon: \"\\uD83D\\uDD0D\",\n                count: 12,\n                color: \"blue\",\n                featured: true\n            },\n            {\n                id: \"content\",\n                name: \"Content Creation\",\n                description: \"Blog posts, social media, and marketing content\",\n                icon: \"✍️\",\n                count: 18,\n                color: \"purple\",\n                featured: true\n            },\n            {\n                id: \"support\",\n                name: \"Customer Support\",\n                description: \"Automated support, ticket routing, and responses\",\n                icon: \"\\uD83C\\uDFA7\",\n                count: 8,\n                color: \"emerald\",\n                featured: true\n            },\n            {\n                id: \"sales\",\n                name: \"Sales & Marketing\",\n                description: \"Lead generation, outreach, and sales automation\",\n                icon: \"\\uD83D\\uDCC8\",\n                count: 15,\n                color: \"orange\",\n                featured: true\n            },\n            {\n                id: \"finance\",\n                name: \"Financial Analysis\",\n                description: \"Financial modeling, risk assessment, and reporting\",\n                icon: \"\\uD83D\\uDCB0\",\n                count: 6,\n                color: \"green\"\n            },\n            {\n                id: \"development\",\n                name: \"Development\",\n                description: \"Code review, testing, and technical documentation\",\n                icon: \"\\uD83D\\uDCBB\",\n                count: 10,\n                color: \"indigo\"\n            }\n        ];\n        const mockTemplates = [\n            {\n                id: \"1\",\n                name: \"Market Research Pro\",\n                description: \"Comprehensive market analysis with competitive intelligence, trend forecasting, and detailed reporting\",\n                category: \"research\",\n                difficulty: \"intermediate\",\n                estimatedTime: \"2-3 hours\",\n                usageCount: 1247,\n                rating: 4.8,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),\n                featured: true,\n                tags: [\n                    \"market-analysis\",\n                    \"competitive-intelligence\",\n                    \"research\",\n                    \"reporting\"\n                ],\n                agents: [\n                    {\n                        id: \"1\",\n                        role: \"Senior Market Researcher\",\n                        goal: \"Conduct comprehensive market research and competitive analysis\",\n                        backstory: \"Expert market researcher with 15+ years experience in analyzing market trends, competitor strategies, and consumer behavior across various industries.\",\n                        tools: [\n                            \"web_search\",\n                            \"data_analysis\",\n                            \"competitive_analysis\",\n                            \"trend_analysis\",\n                            \"report_generation\"\n                        ]\n                    },\n                    {\n                        id: \"2\",\n                        role: \"Data Analyst\",\n                        goal: \"Process and analyze market data to extract actionable insights\",\n                        backstory: \"Data science specialist with expertise in statistical analysis, data visualization, and predictive modeling for market intelligence.\",\n                        tools: [\n                            \"data_processing\",\n                            \"statistical_analysis\",\n                            \"visualization\",\n                            \"predictive_modeling\"\n                        ]\n                    },\n                    {\n                        id: \"3\",\n                        role: \"Report Writer\",\n                        goal: \"Create comprehensive and actionable market research reports\",\n                        backstory: \"Professional business writer specializing in market research reports, executive summaries, and strategic recommendations.\",\n                        tools: [\n                            \"report_writing\",\n                            \"executive_summary\",\n                            \"presentation_creation\",\n                            \"strategic_recommendations\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"1\",\n                        description: \"Conduct comprehensive market size and growth analysis\",\n                        expected_output: \"Detailed market size report with growth projections and key drivers\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"2\",\n                        description: \"Analyze top 10 competitors and their strategies\",\n                        expected_output: \"Competitive landscape analysis with SWOT analysis for each competitor\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"3\",\n                        description: \"Generate executive summary and strategic recommendations\",\n                        expected_output: \"Executive summary with actionable strategic recommendations\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"CrewCraft Team\",\n                    verified: true\n                },\n                requirements: [\n                    \"Web search access\",\n                    \"Data analysis tools\",\n                    \"Report generation capabilities\"\n                ],\n                outputs: [\n                    \"Market size analysis\",\n                    \"Competitive landscape report\",\n                    \"Strategic recommendations\",\n                    \"Executive summary\"\n                ]\n            },\n            {\n                id: \"2\",\n                name: \"Content Marketing Pipeline\",\n                description: \"End-to-end content creation from strategy to publication with SEO optimization and social media distribution\",\n                category: \"content\",\n                difficulty: \"beginner\",\n                estimatedTime: \"1-2 hours\",\n                usageCount: 2156,\n                rating: 4.9,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),\n                featured: true,\n                tags: [\n                    \"content-creation\",\n                    \"seo\",\n                    \"social-media\",\n                    \"marketing\",\n                    \"automation\"\n                ],\n                agents: [\n                    {\n                        id: \"4\",\n                        role: \"Content Strategist\",\n                        goal: \"Develop comprehensive content strategy and editorial calendar\",\n                        backstory: \"Marketing strategist with expertise in content planning, audience analysis, and brand voice development.\",\n                        tools: [\n                            \"content_strategy\",\n                            \"audience_analysis\",\n                            \"editorial_calendar\",\n                            \"brand_voice\"\n                        ]\n                    },\n                    {\n                        id: \"5\",\n                        role: \"SEO Content Writer\",\n                        goal: \"Create engaging, SEO-optimized content across multiple formats\",\n                        backstory: \"Professional copywriter with deep SEO knowledge and experience in various content formats and industries.\",\n                        tools: [\n                            \"content_writing\",\n                            \"seo_optimization\",\n                            \"keyword_research\",\n                            \"content_formatting\"\n                        ]\n                    },\n                    {\n                        id: \"6\",\n                        role: \"Social Media Manager\",\n                        goal: \"Adapt content for social media platforms and manage distribution\",\n                        backstory: \"Social media expert with experience in platform-specific content optimization and community management.\",\n                        tools: [\n                            \"social_media_optimization\",\n                            \"platform_adaptation\",\n                            \"hashtag_research\",\n                            \"engagement_optimization\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"4\",\n                        description: \"Develop content strategy and editorial calendar\",\n                        expected_output: \"Monthly content calendar with topics, keywords, and distribution plan\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"5\",\n                        description: \"Create SEO-optimized blog posts and articles\",\n                        expected_output: \"High-quality, SEO-optimized content pieces ready for publication\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"6\",\n                        description: \"Adapt content for social media platforms\",\n                        expected_output: \"Platform-specific social media posts with optimal hashtags and timing\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"Marketing Experts\",\n                    verified: true\n                },\n                requirements: [\n                    \"Content writing tools\",\n                    \"SEO analysis\",\n                    \"Social media access\"\n                ],\n                outputs: [\n                    \"Content strategy\",\n                    \"SEO-optimized articles\",\n                    \"Social media posts\",\n                    \"Editorial calendar\"\n                ]\n            },\n            {\n                id: \"3\",\n                name: \"Customer Support Automation\",\n                description: \"Intelligent customer support system with ticket classification, automated responses, and escalation management\",\n                category: \"support\",\n                difficulty: \"advanced\",\n                estimatedTime: \"3-4 hours\",\n                usageCount: 892,\n                rating: 4.7,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),\n                tags: [\n                    \"customer-support\",\n                    \"automation\",\n                    \"ai-responses\",\n                    \"ticket-management\"\n                ],\n                agents: [\n                    {\n                        id: \"7\",\n                        role: \"Support Ticket Classifier\",\n                        goal: \"Automatically classify and route support tickets based on content and urgency\",\n                        backstory: \"AI specialist trained in natural language processing and customer service workflows.\",\n                        tools: [\n                            \"text_classification\",\n                            \"sentiment_analysis\",\n                            \"priority_assessment\",\n                            \"routing_logic\"\n                        ]\n                    },\n                    {\n                        id: \"8\",\n                        role: \"Response Generator\",\n                        goal: \"Generate helpful, personalized responses to customer inquiries\",\n                        backstory: \"Customer service expert with extensive knowledge base and communication skills.\",\n                        tools: [\n                            \"response_generation\",\n                            \"knowledge_base_search\",\n                            \"personalization\",\n                            \"tone_adjustment\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"7\",\n                        description: \"Set up automated ticket classification and routing\",\n                        expected_output: \"Intelligent ticket routing system with priority and category assignment\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"8\",\n                        description: \"Generate contextual customer responses\",\n                        expected_output: \"Personalized, helpful responses to customer inquiries\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"Support Team\",\n                    verified: true\n                },\n                requirements: [\n                    \"NLP capabilities\",\n                    \"Knowledge base access\",\n                    \"Ticket system integration\"\n                ],\n                outputs: [\n                    \"Automated ticket routing\",\n                    \"AI-generated responses\",\n                    \"Escalation alerts\",\n                    \"Performance metrics\"\n                ]\n            },\n            {\n                id: \"4\",\n                name: \"Sales Lead Generator\",\n                description: \"Automated lead generation and qualification system with personalized outreach sequences\",\n                category: \"sales\",\n                difficulty: \"intermediate\",\n                estimatedTime: \"2-3 hours\",\n                usageCount: 1543,\n                rating: 4.6,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(),\n                tags: [\n                    \"lead-generation\",\n                    \"sales-automation\",\n                    \"outreach\",\n                    \"qualification\"\n                ],\n                agents: [\n                    {\n                        id: \"9\",\n                        role: \"Lead Researcher\",\n                        goal: \"Find and qualify potential leads based on specified criteria\",\n                        backstory: \"Sales development expert with experience in lead research and qualification across various industries.\",\n                        tools: [\n                            \"lead_research\",\n                            \"company_analysis\",\n                            \"contact_discovery\",\n                            \"qualification_scoring\"\n                        ]\n                    },\n                    {\n                        id: \"10\",\n                        role: \"Outreach Specialist\",\n                        goal: \"Create personalized outreach messages and follow-up sequences\",\n                        backstory: \"Sales communication expert specializing in cold outreach and relationship building.\",\n                        tools: [\n                            \"message_personalization\",\n                            \"sequence_creation\",\n                            \"follow_up_automation\",\n                            \"response_tracking\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"9\",\n                        description: \"Research and qualify potential leads\",\n                        expected_output: \"Qualified lead list with contact information and qualification scores\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"10\",\n                        description: \"Create personalized outreach campaigns\",\n                        expected_output: \"Personalized email sequences with follow-up automation\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"Sales Team\",\n                    verified: true\n                },\n                requirements: [\n                    \"Lead research tools\",\n                    \"Email automation\",\n                    \"CRM integration\"\n                ],\n                outputs: [\n                    \"Qualified lead lists\",\n                    \"Personalized outreach messages\",\n                    \"Follow-up sequences\",\n                    \"Response tracking\"\n                ]\n            }\n        ];\n        setCategories(mockCategories);\n        setTemplates(mockTemplates);\n    }\n    return {\n        templates,\n        categories,\n        isLoading,\n        error,\n        refetch: fetchTemplates\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/use-templates.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasSupabaseCredentials: () => (/* binding */ hasSupabaseCredentials),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Use fallback values for development when env vars are not set\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || \"https://placeholder.supabase.co\";\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || \"placeholder-key\";\n// Create a mock client if no real credentials are provided\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Helper to check if we have real Supabase credentials\nconst hasSupabaseCredentials = ()=>{\n    return process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY && process.env.NEXT_PUBLIC_SUPABASE_URL !== \"https://placeholder.supabase.co\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"33e1a610ba87\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9hcHAvZ2xvYmFscy5jc3M/NjgyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzZTFhNjEwYmE4N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"CrewCraft AI Platform - Multi-Agent AI Orchestration\",\n    description: \"Create, manage, and deploy AI agent crews with Cerebras ultra-fast inference. Build sophisticated multi-agent workflows for complex tasks.\",\n    keywords: \"AI, CrewAI, Cerebras, Multi-Agent, Artificial Intelligence, Automation, Workflows\",\n    authors: [\n        {\n            name: \"CrewCraft Team\"\n        }\n    ],\n    openGraph: {\n        title: \"CrewCraft AI Platform\",\n        description: \"Enterprise-grade multi-agent AI orchestration platform\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-slate-50 text-slate-800\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            className: \"bg-white shadow-lg border border-slate-200\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFNTUE7QUFLQUM7QUFUZ0I7QUFDNEI7QUFDVDtBQVlsQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBaUI7S0FBRTtJQUNyQ0MsV0FBVztRQUNUTCxPQUFPO1FBQ1BDLGFBQWE7UUFDYkssTUFBTTtJQUNSO0FBQ0YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLFdBQVcsQ0FBQyxFQUFFaEIsOEtBQWMsQ0FBQyxDQUFDLEVBQUVDLHdNQUFzQixDQUFDLENBQUM7a0JBQ3RFLDRFQUFDaUI7WUFBS0YsV0FBVTtzQkFDZCw0RUFBQ2QsNERBQVNBOztvQkFDUFc7a0NBQ0QsOERBQUNWLG9EQUFPQTt3QkFDTmdCLFVBQVM7d0JBQ1RDLGNBQWM7NEJBQ1pDLFVBQVU7NEJBQ1ZMLFdBQVc7d0JBQ2I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciwgSmV0QnJhaW5zX01vbm8gfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnQC9jb21wb25lbnRzL3Byb3ZpZGVycydcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBcbiAgc3Vic2V0czogWydsYXRpbiddLFxuICB2YXJpYWJsZTogJy0tZm9udC1pbnRlcicsXG59KVxuXG5jb25zdCBqZXRicmFpbnNNb25vID0gSmV0QnJhaW5zX01vbm8oe1xuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIHZhcmlhYmxlOiAnLS1mb250LWpldGJyYWlucy1tb25vJyxcbn0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ3Jld0NyYWZ0IEFJIFBsYXRmb3JtIC0gTXVsdGktQWdlbnQgQUkgT3JjaGVzdHJhdGlvbicsXG4gIGRlc2NyaXB0aW9uOiAnQ3JlYXRlLCBtYW5hZ2UsIGFuZCBkZXBsb3kgQUkgYWdlbnQgY3Jld3Mgd2l0aCBDZXJlYnJhcyB1bHRyYS1mYXN0IGluZmVyZW5jZS4gQnVpbGQgc29waGlzdGljYXRlZCBtdWx0aS1hZ2VudCB3b3JrZmxvd3MgZm9yIGNvbXBsZXggdGFza3MuJyxcbiAga2V5d29yZHM6ICdBSSwgQ3Jld0FJLCBDZXJlYnJhcywgTXVsdGktQWdlbnQsIEFydGlmaWNpYWwgSW50ZWxsaWdlbmNlLCBBdXRvbWF0aW9uLCBXb3JrZmxvd3MnLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnQ3Jld0NyYWZ0IFRlYW0nIH1dLFxuICBvcGVuR3JhcGg6IHtcbiAgICB0aXRsZTogJ0NyZXdDcmFmdCBBSSBQbGF0Zm9ybScsXG4gICAgZGVzY3JpcHRpb246ICdFbnRlcnByaXNlLWdyYWRlIG11bHRpLWFnZW50IEFJIG9yY2hlc3RyYXRpb24gcGxhdGZvcm0nLFxuICAgIHR5cGU6ICd3ZWJzaXRlJyxcbiAgfSxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPXtgJHtpbnRlci52YXJpYWJsZX0gJHtqZXRicmFpbnNNb25vLnZhcmlhYmxlfWB9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGFudGlhbGlhc2VkIGJnLXNsYXRlLTUwIHRleHQtc2xhdGUtODAwXCI+XG4gICAgICAgIDxQcm92aWRlcnM+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxUb2FzdGVyXG4gICAgICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgICAgICAgIGNsYXNzTmFtZTogJ2JnLXdoaXRlIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLXNsYXRlLTIwMCcsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiamV0YnJhaW5zTW9ubyIsIlByb3ZpZGVycyIsIlRvYXN0ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwib3BlbkdyYXBoIiwidHlwZSIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJib2R5IiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/templates/page.tsx":
/*!********************************!*\
  !*** ./app/templates/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e1),
/* harmony export */   useAppContext: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#useAppContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/@headlessui","vendor-chunks/@heroicons","vendor-chunks/clsx","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();