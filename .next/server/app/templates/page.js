/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/templates/page";
exports.ids = ["app/templates/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'templates',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/templates/page.tsx */ \"(rsc)/./app/templates/page.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/templates/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/templates/page\",\n        pathname: \"/templates\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Ftemplates%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Ftemplates%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/templates/page.tsx */ \"(ssr)/./app/templates/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZhcHAlMkZ0ZW1wbGF0ZXMlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vP2Y5YTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2FwcC90ZW1wbGF0ZXMvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Ftemplates%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGcmFqc2hhaCUyRkRvd25sb2FkcyUyRlByb2plY3RzJTJGQUlDcmV3RGVja2VyJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySmV0QnJhaW5zX01vbm8lMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlMkMlMjJ2YXJpYWJsZSUyMiUzQSUyMi0tZm9udC1qZXRicmFpbnMtbW9ubyUyMiU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmpldGJyYWluc01vbm8lMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnJhanNoYWglMkZEb3dubG9hZHMlMkZQcm9qZWN0cyUyRkFJQ3Jld0RlY2tlciUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZyYWpzaGFoJTJGRG93bmxvYWRzJTJGUHJvamVjdHMlMkZBSUNyZXdEZWNrZXIlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBNEc7QUFDNUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vP2MxYTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcmFqc2hhaC9Eb3dubG9hZHMvUHJvamVjdHMvQUlDcmV3RGVja2VyL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhanNoYWgvRG93bmxvYWRzL1Byb2plY3RzL0FJQ3Jld0RlY2tlci9ub2RlX21vZHVsZXMvcmVhY3QtaG90LXRvYXN0L2Rpc3QvaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fcomponents%2Fproviders.tsx&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-jetbrains-mono%22%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp%2Fglobals.css&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/templates/page.tsx":
/*!********************************!*\
  !*** ./app/templates/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplatesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(ssr)/./components/layout/sidebar.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/header */ \"(ssr)/./components/layout/header.tsx\");\n/* harmony import */ var _components_templates_template_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/templates/template-card */ \"(ssr)/./components/templates/template-card.tsx\");\n/* harmony import */ var _components_templates_template_filters__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/templates/template-filters */ \"(ssr)/./components/templates/template-filters.tsx\");\n/* harmony import */ var _components_templates_template_categories__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/templates/template-categories */ \"(ssr)/./components/templates/template-categories.tsx\");\n/* harmony import */ var _lib_hooks_use_templates__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/hooks/use-templates */ \"(ssr)/./lib/hooks/use-templates.ts\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=FireIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,SparklesIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction TemplatesPage() {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedDifficulty, setSelectedDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"popular\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { templates, categories, isLoading } = (0,_lib_hooks_use_templates__WEBPACK_IMPORTED_MODULE_7__.useTemplates)();\n    const filteredTemplates = templates.filter((template)=>{\n        const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) || template.description.toLowerCase().includes(searchQuery.toLowerCase()) || template.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || template.category === selectedCategory;\n        const matchesDifficulty = selectedDifficulty === \"all\" || template.difficulty === selectedDifficulty;\n        return matchesSearch && matchesCategory && matchesDifficulty;\n    });\n    const sortedTemplates = [\n        ...filteredTemplates\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case \"popular\":\n                return b.usageCount - a.usageCount;\n            case \"rating\":\n                return b.rating - a.rating;\n            case \"newest\":\n                return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n            case \"name\":\n                return a.name.localeCompare(b.name);\n            default:\n                return 0;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_3__.Header, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary-100 to-primary-300 rounded-full text-primary-700 text-sm font-medium mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 13\n                                                }, this),\n                                                \"Template Gallery\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl font-bold text-slate-900 mb-4\",\n                                            children: \"Ready-to-Use AI Crew Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-slate-600 max-w-3xl mx-auto\",\n                                            children: \"Jumpstart your AI projects with professionally crafted crew templates. From research and content creation to customer support and analytics.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_templates_template_categories__WEBPACK_IMPORTED_MODULE_6__.TemplateCategories, {\n                                        categories: categories,\n                                        selectedCategory: selectedCategory,\n                                        onCategorySelect: setSelectedCategory\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    className: \"bg-white rounded-xl border border-slate-200 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col lg:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search templates by name, description, or tags...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"w-full pl-10 pr-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: sortBy,\n                                                    onChange: (e)=>setSortBy(e.target.value),\n                                                    className: \"px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"popular\",\n                                                            children: \"Most Popular\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"rating\",\n                                                            children: \"Highest Rated\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"newest\",\n                                                            children: \"Newest\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: \"Name A-Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedDifficulty,\n                                                    onChange: (e)=>setSelectedDifficulty(e.target.value),\n                                                    className: \"px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"all\",\n                                                            children: \"All Levels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"beginner\",\n                                                            children: \"Beginner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"intermediate\",\n                                                            children: \"Intermediate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"advanced\",\n                                                            children: \"Advanced\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center bg-slate-100 rounded-lg p-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewMode(\"grid\"),\n                                                            className: `p-2 rounded-md transition-colors ${viewMode === \"grid\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-slate-600 hover:text-slate-900\"}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewMode(\"list\"),\n                                                            className: `p-2 rounded-md transition-colors ${viewMode === \"list\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-slate-600 hover:text-slate-900\"}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(!showFilters),\n                                                    className: \"inline-flex items-center gap-2 px-4 py-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        \"Filters\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 11\n                                        }, this),\n                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"mt-6 pt-6 border-t border-slate-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_templates_template_filters__WEBPACK_IMPORTED_MODULE_5__.TemplateFilters, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-600\",\n                                                    children: [\n                                                        \"Showing \",\n                                                        sortedTemplates.length,\n                                                        \" of \",\n                                                        templates.length,\n                                                        \" templates\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 13\n                                                }, this),\n                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                    className: \"text-sm text-primary-600 hover:text-primary-700\",\n                                                    children: \"Clear search\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-amber-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Trending templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.4\n                                    },\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `grid gap-6 ${viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 xl:grid-cols-3\" : \"grid-cols-1\"}`,\n                                        children: [\n                                            ...Array(6)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl border border-slate-200 p-6 animate-pulse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-slate-200 rounded w-3/4 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-slate-200 rounded w-full mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-slate-200 rounded w-2/3 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 bg-slate-200 rounded w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 bg-slate-200 rounded w-20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this) : sortedTemplates.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `grid gap-6 ${viewMode === \"grid\" ? \"grid-cols-1 md:grid-cols-2 xl:grid-cols-3\" : \"grid-cols-1\"}`,\n                                        children: sortedTemplates.map((template, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.95\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    delay: index * 0.05\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_templates_template_card__WEBPACK_IMPORTED_MODULE_4__.TemplateCard, {\n                                                    template: template,\n                                                    viewMode: viewMode\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, template.id, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-24 w-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FireIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_SparklesIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-12 w-12 text-slate-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-slate-900 mb-2\",\n                                                children: \"No templates found\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 mb-6\",\n                                                children: \"Try adjusting your search or filters to find what you're looking for\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setSearchQuery(\"\");\n                                                    setSelectedCategory(\"all\");\n                                                    setSelectedDifficulty(\"all\");\n                                                },\n                                                className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                                                children: \"Clear all filters\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 9\n                                }, this),\n                                sortedTemplates.length > 0 && sortedTemplates.length < templates.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors\",\n                                        children: \"Load More Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/templates/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/header.tsx":
/*!**************************************!*\
  !*** ./components/layout/header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Monitor,Play,Save,Search,Share2,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/workflow-store */ \"(ssr)/./store/workflow-store.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header() {\n    const { isExecuting, isExecutionPanelOpen, toggleExecutionPanel, startExecution, stopExecution } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_1__.useWorkflowStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.header, {\n        initial: {\n            y: -60\n        },\n        animate: {\n            y: 0\n        },\n        className: \"h-16 glass border-b border-white/20 flex items-center justify-between px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-slate-900\",\n                                children: \"Content Pipeline\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full\",\n                                children: \"Draft\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-white/50 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-white/50 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 max-w-md mx-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Search agents, tools, or templates...\",\n                            className: \"w-full pl-10 pr-4 py-2 bg-white/50 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: !isExecuting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            onClick: startExecution,\n                            className: \"flex items-center space-x-2 px-4 py-2 gradient-primary text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Deploy Crew\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stopExecution,\n                                    className: \"flex items-center space-x-2 px-3 py-2 bg-red-500 text-white rounded-lg font-medium hover:bg-red-600 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Stop\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: \"Running\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleExecutionPanel,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-2 rounded-lg transition-colors\", isExecutionPanelOpen ? \"bg-primary-100 text-primary-700\" : \"text-slate-600 hover:text-slate-900 hover:bg-white/50\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"relative p-2 text-slate-600 hover:text-slate-900 hover:bg-white/50 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Monitor_Play_Save_Search_Share2_Square_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/header.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,FileText,Plus,Settings,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/workflow-store */ \"(ssr)/./store/workflow-store.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Workflows\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: \"/workflow\"\n    },\n    {\n        name: \"Templates\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        href: \"/templates\"\n    },\n    {\n        name: \"Analytics\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: \"/analytics\"\n    },\n    {\n        name: \"Team\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: \"/team\"\n    },\n    {\n        name: \"Settings\",\n        icon: _barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: \"/settings\"\n    }\n];\nconst recentWorkflows = [\n    {\n        name: \"Content Pipeline\",\n        status: \"active\",\n        agents: 3\n    },\n    {\n        name: \"Research Crew\",\n        status: \"idle\",\n        agents: 2\n    },\n    {\n        name: \"Data Analysis\",\n        status: \"running\",\n        agents: 4\n    }\n];\nfunction Sidebar() {\n    const { selectedWorkflow, setSelectedWorkflow } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_1__.useWorkflowStore)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            x: -250\n        },\n        animate: {\n            x: 0\n        },\n        className: \"w-64 bg-white border-r border-slate-200 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 gradient-primary rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-bold text-slate-900\",\n                                    children: \"CrewBuilder\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: \"AI Workflow Platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center space-x-2 px-4 py-3 gradient-primary text-white rounded-lg hover:opacity-90 transition-opacity\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_FileText_Plus_Settings_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"New Workflow\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    navigation.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: item.href,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                whileHover: {\n                                    x: 4\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer\", isActive ? \"bg-primary-50 text-primary-700 border border-primary-200\" : \"text-slate-600 hover:text-slate-900 hover:bg-slate-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-slate-500 uppercase tracking-wider mb-3\",\n                                children: \"Recent Workflows\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: recentWorkflows.map((workflow)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        whileHover: {\n                                            x: 4\n                                        },\n                                        className: \"flex items-center justify-between p-2 rounded-lg hover:bg-slate-50 cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-primary-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-slate-900\",\n                                                                children: workflow.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: [\n                                                                    workflow.agents,\n                                                                    \" agents\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-2 h-2 rounded-full\", workflow.status === \"active\" ? \"bg-green-500\" : workflow.status === \"running\" ? \"bg-blue-500 animate-pulse\" : \"bg-slate-300\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, workflow.name, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-slate-900\",\n                                    children: \"John Doe\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: \"Pro Plan\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/layout/sidebar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useAppContext: () => (/* binding */ useAppContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAppContext,Providers auto */ \n\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAppContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (context === undefined) {\n        throw new Error(\"useAppContext must be used within a Providers component\");\n    }\n    return context;\n}\nfunction Providers({ children }) {\n    const value = {\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0RDtBQVU1RCxNQUFNRSwyQkFBYUYsb0RBQWFBLENBQTZCRztBQUV0RCxTQUFTQztJQUNkLE1BQU1DLFVBQVVKLGlEQUFVQSxDQUFDQztJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1Q7QUFFTyxTQUFTRSxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQsTUFBTUMsUUFBd0I7SUFFOUI7SUFFQSxxQkFDRSw4REFBQ1AsV0FBV1EsUUFBUTtRQUFDRCxPQUFPQTtrQkFDekJEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZXdjcmFmdC1haS1wbGF0Zm9ybS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5pbnRlcmZhY2UgQXBwQ29udGV4dFR5cGUge1xuICAvLyBBZGQgYW55IGdsb2JhbCBzdGF0ZSBvciBmdW5jdGlvbnMgaGVyZVxufVxuXG5jb25zdCBBcHBDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBcHBDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBwQ29udGV4dCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXBwQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXBwQ29udGV4dCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgUHJvdmlkZXJzIGNvbXBvbmVudCcpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIGNvbnN0IHZhbHVlOiBBcHBDb250ZXh0VHlwZSA9IHtcbiAgICAvLyBJbml0aWFsaXplIGNvbnRleHQgdmFsdWVzIGhlcmVcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEFwcENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0FwcENvbnRleHQuUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsIkFwcENvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VBcHBDb250ZXh0IiwiY29udGV4dCIsIkVycm9yIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/templates/template-card.tsx":
/*!************************************************!*\
  !*** ./components/templates/template-card.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCard: () => (/* binding */ TemplateCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckBadgeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BookmarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,CheckBadgeIcon,ClockIcon,EyeIcon,HeartIcon,PlayIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/BookmarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookmarkIcon,HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ TemplateCard auto */ \n\n\n\n\n\n\nfunction TemplateCard({ template, viewMode }) {\n    const [isBookmarked, setIsBookmarked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const difficultyColors = {\n        beginner: \"bg-green-100 text-green-700 border-green-200\",\n        intermediate: \"bg-yellow-100 text-yellow-700 border-yellow-200\",\n        advanced: \"bg-red-100 text-red-700 border-red-200\"\n    };\n    const categoryColors = {\n        research: \"bg-blue-100 text-blue-700\",\n        content: \"bg-purple-100 text-purple-700\",\n        support: \"bg-green-100 text-green-700\",\n        analytics: \"bg-orange-100 text-orange-700\",\n        sales: \"bg-pink-100 text-pink-700\",\n        marketing: \"bg-indigo-100 text-indigo-700\"\n    };\n    if (viewMode === \"list\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            whileHover: {\n                scale: 1.01\n            },\n            className: \"bg-white rounded-xl border border-slate-200 p-6 hover:shadow-lg transition-all duration-200\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 w-24 h-24 bg-gradient-to-br from-primary-100 to-purple-100 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-10 w-10 text-primary-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-slate-900 truncate\",\n                                                        children: template.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    template.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm line-clamp-2\",\n                                                children: template.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsBookmarked(!isBookmarked),\n                                                className: \"p-2 text-slate-400 hover:text-primary-600 transition-colors\",\n                                                children: isBookmarked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsLiked(!isLiked),\n                                                className: \"p-2 text-slate-400 hover:text-red-500 transition-colors\",\n                                                children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/templates/${template.id}`,\n                                                className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Use Template\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 text-sm text-slate-500 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: template.rating\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    template.usageCount.toLocaleString(),\n                                                    \" uses\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: template.estimatedTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    template.agents.length,\n                                                    \" agents\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full border\", difficultyColors[template.difficulty]),\n                                        children: template.difficulty\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full\", categoryColors[template.category] || \"bg-slate-100 text-slate-700\"),\n                                        children: template.category\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    template.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs text-slate-600 bg-slate-100 rounded-full\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)),\n                                    template.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-slate-500\",\n                                        children: [\n                                            \"+\",\n                                            template.tags.length - 3,\n                                            \" more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        whileHover: {\n            scale: 1.02\n        },\n        className: \"bg-white rounded-xl border border-slate-200 overflow-hidden hover:shadow-lg transition-all duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-slate-900 line-clamp-1\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    template.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsBookmarked(!isBookmarked),\n                                        className: \"p-1 text-slate-400 hover:text-primary-600 transition-colors\",\n                                        children: isBookmarked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsLiked(!isLiked),\n                                        className: \"p-1 text-slate-400 hover:text-red-500 transition-colors\",\n                                        children: isLiked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-600 text-sm line-clamp-3 mb-4\",\n                        children: template.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 text-sm text-slate-500 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: template.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: template.usageCount.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: template.estimatedTime\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 flex-wrap mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full border\", difficultyColors[template.difficulty]),\n                                children: template.difficulty\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(\"px-2 py-1 text-xs font-medium rounded-full\", categoryColors[template.category] || \"bg-slate-100 text-slate-700\"),\n                                children: template.category\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            template.tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs text-slate-600 bg-slate-100 rounded-full\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)),\n                            template.tags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-slate-500\",\n                                children: [\n                                    \"+\",\n                                    template.tags.length - 2\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 bg-slate-50 border-t border-slate-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm text-slate-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        template.agents.length,\n                                        \" agents\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: `/templates/${template.id}`,\n                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookmarkIcon_CheckBadgeIcon_ClockIcon_EyeIcon_HeartIcon_PlayIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Template\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-card.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/templates/template-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/templates/template-categories.tsx":
/*!******************************************************!*\
  !*** ./components/templates/template-categories.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateCategories: () => (/* binding */ TemplateCategories)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MegaphoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,ChatBubbleLeftRightIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,MagnifyingGlassIcon,MegaphoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ TemplateCategories auto */ \n\n\n\nconst categoryIcons = {\n    research: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    content: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    support: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    analytics: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    sales: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    marketing: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    automation: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    all: _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\nconst categoryColors = {\n    research: \"from-blue-500 to-cyan-500\",\n    content: \"from-purple-500 to-pink-500\",\n    support: \"from-green-500 to-emerald-500\",\n    analytics: \"from-orange-500 to-red-500\",\n    sales: \"from-pink-500 to-rose-500\",\n    marketing: \"from-indigo-500 to-purple-500\",\n    automation: \"from-gray-500 to-slate-500\",\n    all: \"from-primary-500 to-primary-600\"\n};\nfunction TemplateCategories({ categories, selectedCategory, onCategorySelect }) {\n    // Add \"All\" category at the beginning\n    const allCategories = [\n        {\n            id: \"all\",\n            name: \"All Templates\",\n            description: \"Browse all available templates\",\n            icon: \"all\",\n            count: categories.reduce((sum, cat)=>sum + cat.count, 0)\n        },\n        ...categories\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4\",\n                children: allCategories.map((category, index)=>{\n                    const IconComponent = categoryIcons[category.id] || _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                    const isSelected = selectedCategory === category.id;\n                    const gradientClass = categoryColors[category.id] || categoryColors.all;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.05\n                        },\n                        onClick: ()=>onCategorySelect(category.id),\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"group relative p-4 rounded-xl border-2 transition-all duration-200 text-center\", isSelected ? \"border-primary-300 bg-primary-50 shadow-lg scale-105\" : \"border-slate-200 bg-white hover:border-slate-300 hover:shadow-md hover:scale-102\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"absolute inset-0 rounded-xl bg-gradient-to-br opacity-0 transition-opacity duration-200\", gradientClass, isSelected ? \"opacity-10\" : \"group-hover:opacity-5\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"relative w-12 h-12 mx-auto mb-3 rounded-lg flex items-center justify-center transition-all duration-200\", isSelected ? `bg-gradient-to-br ${gradientClass} text-white shadow-lg` : \"bg-slate-100 text-slate-600 group-hover:bg-slate-200\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"text-sm font-medium mb-1 transition-colors duration-200\", isSelected ? \"text-primary-900\" : \"text-slate-900 group-hover:text-slate-700\"),\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"text-xs transition-colors duration-200\", isSelected ? \"text-primary-600\" : \"text-slate-500 group-hover:text-slate-600\"),\n                                        children: [\n                                            category.count,\n                                            \" templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                layoutId: \"categorySelection\",\n                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center\",\n                                transition: {\n                                    type: \"spring\",\n                                    bounce: 0.2,\n                                    duration: 0.6\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            selectedCategory !== \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: \"auto\"\n                },\n                exit: {\n                    opacity: 0,\n                    height: 0\n                },\n                className: \"bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl p-6 border border-slate-200\",\n                children: (()=>{\n                    const category = categories.find((cat)=>cat.id === selectedCategory);\n                    if (!category) return null;\n                    const IconComponent = categoryIcons[category.id] || _barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                    const gradientClass = categoryColors[category.id] || categoryColors.all;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"w-12 h-12 rounded-lg flex items-center justify-center bg-gradient-to-br text-white shadow-lg\", gradientClass),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-slate-900 mb-2\",\n                                        children: [\n                                            category.name,\n                                            \" Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600 mb-3\",\n                                        children: category.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-sm text-slate-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    category.count,\n                                                    \" templates available\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this),\n                                            category.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-700 rounded-full text-xs font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_ChatBubbleLeftRightIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_MagnifyingGlassIcon_MegaphoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Featured Category\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 15\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: allCategories[0].count\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Total Templates\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: categories.filter((cat)=>cat.featured).length\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Featured\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: categories.length\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-slate-200 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-slate-900 mb-1\",\n                                children: \"4.8\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"Avg Rating\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-categories.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/templates/template-categories.tsx\n");

/***/ }),

/***/ "(ssr)/./components/templates/template-filters.tsx":
/*!***************************************************!*\
  !*** ./components/templates/template-filters.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TemplateFilters: () => (/* binding */ TemplateFilters)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ClockIcon,StarIcon,TagIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ TemplateFilters auto */ \n\n\n\n\n\nfunction TemplateFilters({ onFiltersChange }) {\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        minRating: 0,\n        maxEstimatedTime: 0,\n        tags: [],\n        agentCount: {\n            min: 0,\n            max: 10\n        },\n        featured: false,\n        difficulty: [],\n        sortBy: \"popular\"\n    });\n    const popularTags = [\n        \"research\",\n        \"content-creation\",\n        \"customer-support\",\n        \"data-analysis\",\n        \"marketing\",\n        \"sales\",\n        \"automation\",\n        \"reporting\",\n        \"social-media\",\n        \"email-marketing\",\n        \"lead-generation\",\n        \"competitive-analysis\"\n    ];\n    const difficulties = [\n        \"beginner\",\n        \"intermediate\",\n        \"advanced\"\n    ];\n    const estimatedTimes = [\n        {\n            label: \"Under 1 hour\",\n            value: 1\n        },\n        {\n            label: \"1-3 hours\",\n            value: 3\n        },\n        {\n            label: \"3-6 hours\",\n            value: 6\n        },\n        {\n            label: \"6+ hours\",\n            value: 24\n        }\n    ];\n    const updateFilters = (newFilters)=>{\n        const updatedFilters = {\n            ...filters,\n            ...newFilters\n        };\n        setFilters(updatedFilters);\n        onFiltersChange?.(updatedFilters);\n    };\n    const toggleTag = (tag)=>{\n        const newTags = filters.tags.includes(tag) ? filters.tags.filter((t)=>t !== tag) : [\n            ...filters.tags,\n            tag\n        ];\n        updateFilters({\n            tags: newTags\n        });\n    };\n    const toggleDifficulty = (difficulty)=>{\n        const newDifficulties = filters.difficulty.includes(difficulty) ? filters.difficulty.filter((d)=>d !== difficulty) : [\n            ...filters.difficulty,\n            difficulty\n        ];\n        updateFilters({\n            difficulty: newDifficulties\n        });\n    };\n    const clearAllFilters = ()=>{\n        const clearedFilters = {\n            minRating: 0,\n            maxEstimatedTime: 0,\n            tags: [],\n            agentCount: {\n                min: 0,\n                max: 10\n            },\n            featured: false,\n            difficulty: [],\n            sortBy: \"popular\"\n        };\n        setFilters(clearedFilters);\n        onFiltersChange?.(clearedFilters);\n    };\n    const hasActiveFilters = filters.minRating > 0 || filters.maxEstimatedTime > 0 || filters.tags.length > 0 || filters.difficulty.length > 0 || filters.featured || filters.agentCount.min > 0 || filters.agentCount.max < 10;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5 text-slate-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-slate-900\",\n                                children: \"Advanced Filters\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearAllFilters,\n                        className: \"inline-flex items-center gap-1 px-3 py-1 text-sm text-slate-600 hover:text-slate-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            \"Clear all\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-slate-700\",\n                                children: \"Minimum Rating\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    4,\n                                    3,\n                                    2,\n                                    1\n                                ].map((rating)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>updateFilters({\n                                                minRating: rating === filters.minRating ? 0 : rating\n                                            }),\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center gap-2 w-full p-2 rounded-lg border transition-colors\", filters.minRating === rating ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"h-4 w-4\", i < rating ? \"text-yellow-400 fill-current\" : \"text-slate-300\")\n                                                    }, i, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"& up\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, rating, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-slate-700\",\n                                children: \"Difficulty Level\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: difficulties.map((difficulty)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleDifficulty(difficulty),\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center justify-between w-full p-2 rounded-lg border transition-colors\", filters.difficulty.includes(difficulty) ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm capitalize\",\n                                                children: difficulty\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            filters.difficulty.includes(difficulty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, difficulty, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-slate-700\",\n                                children: \"Estimated Time\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: estimatedTimes.map((time)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>updateFilters({\n                                                maxEstimatedTime: time.value === filters.maxEstimatedTime ? 0 : time.value\n                                            }),\n                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center justify-between w-full p-2 rounded-lg border transition-colors\", filters.maxEstimatedTime === time.value ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: time.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            filters.maxEstimatedTime === time.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, time.value, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-slate-700\",\n                        children: \"Number of Agents\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 text-slate-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-slate-600\",\n                                        children: \"Min:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"0\",\n                                        max: \"10\",\n                                        value: filters.agentCount.min,\n                                        onChange: (e)=>updateFilters({\n                                                agentCount: {\n                                                    ...filters.agentCount,\n                                                    min: parseInt(e.target.value) || 0\n                                                }\n                                            }),\n                                        className: \"w-16 px-2 py-1 border border-slate-200 rounded text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-slate-600\",\n                                        children: \"Max:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"0\",\n                                        max: \"10\",\n                                        value: filters.agentCount.max,\n                                        onChange: (e)=>updateFilters({\n                                                agentCount: {\n                                                    ...filters.agentCount,\n                                                    max: parseInt(e.target.value) || 10\n                                                }\n                                            }),\n                                        className: \"w-16 px-2 py-1 border border-slate-200 rounded text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-slate-700\",\n                        children: \"Popular Tags\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: popularTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleTag(tag),\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium transition-colors\", filters.tags.includes(tag) ? \"bg-primary-100 text-primary-700 border border-primary-300\" : \"bg-slate-100 text-slate-700 border border-slate-200 hover:bg-slate-200\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ClockIcon_StarIcon_TagIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    tag\n                                ]\n                            }, tag, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-slate-700\",\n                        children: \"Special Filters\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>updateFilters({\n                                    featured: !filters.featured\n                                }),\n                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center justify-between w-full p-3 rounded-lg border transition-colors\", filters.featured ? \"border-primary-300 bg-primary-50 text-primary-700\" : \"border-slate-200 hover:border-slate-300 text-slate-700\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-amber-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Featured Templates Only\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                filters.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-primary-600 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    height: 0\n                },\n                animate: {\n                    opacity: 1,\n                    height: \"auto\"\n                },\n                className: \"bg-primary-50 border border-primary-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-primary-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            filters.minRating > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                children: [\n                                    \"Rating \",\n                                    filters.minRating,\n                                    \"+ stars\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this),\n                            filters.maxEstimatedTime > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                children: [\n                                    \"Under \",\n                                    filters.maxEstimatedTime,\n                                    \"h\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this),\n                            filters.difficulty.map((diff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                    children: diff\n                                }, diff, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this)),\n                            filters.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                    children: [\n                                        \"#\",\n                                        tag\n                                    ]\n                                }, tag, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)),\n                            filters.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs\",\n                                children: \"Featured\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/components/templates/template-filters.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/templates/template-filters.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/hooks/use-templates.ts":
/*!************************************!*\
  !*** ./lib/hooks/use-templates.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTemplates: () => (/* binding */ useTemplates)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useTemplates auto */ \n\nfunction useTemplates() {\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchTemplates();\n    }, []);\n    async function fetchTemplates() {\n        try {\n            setIsLoading(true);\n            setError(null);\n            // If no Supabase credentials, use mock data immediately\n            if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.hasSupabaseCredentials)()) {\n                console.log(\"Using mock templates data - Supabase credentials not configured\");\n                setMockData();\n                return;\n            }\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from(\"templates\").select(\"*\").order(\"usage_count\", {\n                ascending: false\n            });\n            if (error) throw error;\n            setTemplates(data || []);\n        } catch (err) {\n            console.error(\"Error fetching templates:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch templates\");\n            setMockData();\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    function setMockData() {\n        const mockCategories = [\n            {\n                id: \"research\",\n                name: \"Research & Analysis\",\n                description: \"Data research, market analysis, and competitive intelligence\",\n                icon: \"\\uD83D\\uDD0D\",\n                count: 12,\n                color: \"blue\",\n                featured: true\n            },\n            {\n                id: \"content\",\n                name: \"Content Creation\",\n                description: \"Blog posts, social media, and marketing content\",\n                icon: \"✍️\",\n                count: 18,\n                color: \"purple\",\n                featured: true\n            },\n            {\n                id: \"support\",\n                name: \"Customer Support\",\n                description: \"Automated support, ticket routing, and responses\",\n                icon: \"\\uD83C\\uDFA7\",\n                count: 8,\n                color: \"emerald\",\n                featured: true\n            },\n            {\n                id: \"sales\",\n                name: \"Sales & Marketing\",\n                description: \"Lead generation, outreach, and sales automation\",\n                icon: \"\\uD83D\\uDCC8\",\n                count: 15,\n                color: \"orange\",\n                featured: true\n            },\n            {\n                id: \"finance\",\n                name: \"Financial Analysis\",\n                description: \"Financial modeling, risk assessment, and reporting\",\n                icon: \"\\uD83D\\uDCB0\",\n                count: 6,\n                color: \"green\"\n            },\n            {\n                id: \"development\",\n                name: \"Development\",\n                description: \"Code review, testing, and technical documentation\",\n                icon: \"\\uD83D\\uDCBB\",\n                count: 10,\n                color: \"indigo\"\n            }\n        ];\n        const mockTemplates = [\n            {\n                id: \"1\",\n                name: \"Market Research Pro\",\n                description: \"Comprehensive market analysis with competitive intelligence, trend forecasting, and detailed reporting\",\n                category: \"research\",\n                difficulty: \"intermediate\",\n                estimatedTime: \"2-3 hours\",\n                usageCount: 1247,\n                rating: 4.8,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),\n                featured: true,\n                tags: [\n                    \"market-analysis\",\n                    \"competitive-intelligence\",\n                    \"research\",\n                    \"reporting\"\n                ],\n                agents: [\n                    {\n                        id: \"1\",\n                        role: \"Senior Market Researcher\",\n                        goal: \"Conduct comprehensive market research and competitive analysis\",\n                        backstory: \"Expert market researcher with 15+ years experience in analyzing market trends, competitor strategies, and consumer behavior across various industries.\",\n                        tools: [\n                            \"web_search\",\n                            \"data_analysis\",\n                            \"competitive_analysis\",\n                            \"trend_analysis\",\n                            \"report_generation\"\n                        ]\n                    },\n                    {\n                        id: \"2\",\n                        role: \"Data Analyst\",\n                        goal: \"Process and analyze market data to extract actionable insights\",\n                        backstory: \"Data science specialist with expertise in statistical analysis, data visualization, and predictive modeling for market intelligence.\",\n                        tools: [\n                            \"data_processing\",\n                            \"statistical_analysis\",\n                            \"visualization\",\n                            \"predictive_modeling\"\n                        ]\n                    },\n                    {\n                        id: \"3\",\n                        role: \"Report Writer\",\n                        goal: \"Create comprehensive and actionable market research reports\",\n                        backstory: \"Professional business writer specializing in market research reports, executive summaries, and strategic recommendations.\",\n                        tools: [\n                            \"report_writing\",\n                            \"executive_summary\",\n                            \"presentation_creation\",\n                            \"strategic_recommendations\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"1\",\n                        description: \"Conduct comprehensive market size and growth analysis\",\n                        expected_output: \"Detailed market size report with growth projections and key drivers\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"2\",\n                        description: \"Analyze top 10 competitors and their strategies\",\n                        expected_output: \"Competitive landscape analysis with SWOT analysis for each competitor\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"3\",\n                        description: \"Generate executive summary and strategic recommendations\",\n                        expected_output: \"Executive summary with actionable strategic recommendations\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"CrewCraft Team\",\n                    verified: true\n                },\n                requirements: [\n                    \"Web search access\",\n                    \"Data analysis tools\",\n                    \"Report generation capabilities\"\n                ],\n                outputs: [\n                    \"Market size analysis\",\n                    \"Competitive landscape report\",\n                    \"Strategic recommendations\",\n                    \"Executive summary\"\n                ]\n            },\n            {\n                id: \"2\",\n                name: \"Content Marketing Pipeline\",\n                description: \"End-to-end content creation from strategy to publication with SEO optimization and social media distribution\",\n                category: \"content\",\n                difficulty: \"beginner\",\n                estimatedTime: \"1-2 hours\",\n                usageCount: 2156,\n                rating: 4.9,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),\n                featured: true,\n                tags: [\n                    \"content-creation\",\n                    \"seo\",\n                    \"social-media\",\n                    \"marketing\",\n                    \"automation\"\n                ],\n                agents: [\n                    {\n                        id: \"4\",\n                        role: \"Content Strategist\",\n                        goal: \"Develop comprehensive content strategy and editorial calendar\",\n                        backstory: \"Marketing strategist with expertise in content planning, audience analysis, and brand voice development.\",\n                        tools: [\n                            \"content_strategy\",\n                            \"audience_analysis\",\n                            \"editorial_calendar\",\n                            \"brand_voice\"\n                        ]\n                    },\n                    {\n                        id: \"5\",\n                        role: \"SEO Content Writer\",\n                        goal: \"Create engaging, SEO-optimized content across multiple formats\",\n                        backstory: \"Professional copywriter with deep SEO knowledge and experience in various content formats and industries.\",\n                        tools: [\n                            \"content_writing\",\n                            \"seo_optimization\",\n                            \"keyword_research\",\n                            \"content_formatting\"\n                        ]\n                    },\n                    {\n                        id: \"6\",\n                        role: \"Social Media Manager\",\n                        goal: \"Adapt content for social media platforms and manage distribution\",\n                        backstory: \"Social media expert with experience in platform-specific content optimization and community management.\",\n                        tools: [\n                            \"social_media_optimization\",\n                            \"platform_adaptation\",\n                            \"hashtag_research\",\n                            \"engagement_optimization\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"4\",\n                        description: \"Develop content strategy and editorial calendar\",\n                        expected_output: \"Monthly content calendar with topics, keywords, and distribution plan\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"5\",\n                        description: \"Create SEO-optimized blog posts and articles\",\n                        expected_output: \"High-quality, SEO-optimized content pieces ready for publication\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"6\",\n                        description: \"Adapt content for social media platforms\",\n                        expected_output: \"Platform-specific social media posts with optimal hashtags and timing\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"Marketing Experts\",\n                    verified: true\n                },\n                requirements: [\n                    \"Content writing tools\",\n                    \"SEO analysis\",\n                    \"Social media access\"\n                ],\n                outputs: [\n                    \"Content strategy\",\n                    \"SEO-optimized articles\",\n                    \"Social media posts\",\n                    \"Editorial calendar\"\n                ]\n            },\n            {\n                id: \"3\",\n                name: \"Customer Support Automation\",\n                description: \"Intelligent customer support system with ticket classification, automated responses, and escalation management\",\n                category: \"support\",\n                difficulty: \"advanced\",\n                estimatedTime: \"3-4 hours\",\n                usageCount: 892,\n                rating: 4.7,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),\n                tags: [\n                    \"customer-support\",\n                    \"automation\",\n                    \"ai-responses\",\n                    \"ticket-management\"\n                ],\n                agents: [\n                    {\n                        id: \"7\",\n                        role: \"Support Ticket Classifier\",\n                        goal: \"Automatically classify and route support tickets based on content and urgency\",\n                        backstory: \"AI specialist trained in natural language processing and customer service workflows.\",\n                        tools: [\n                            \"text_classification\",\n                            \"sentiment_analysis\",\n                            \"priority_assessment\",\n                            \"routing_logic\"\n                        ]\n                    },\n                    {\n                        id: \"8\",\n                        role: \"Response Generator\",\n                        goal: \"Generate helpful, personalized responses to customer inquiries\",\n                        backstory: \"Customer service expert with extensive knowledge base and communication skills.\",\n                        tools: [\n                            \"response_generation\",\n                            \"knowledge_base_search\",\n                            \"personalization\",\n                            \"tone_adjustment\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"7\",\n                        description: \"Set up automated ticket classification and routing\",\n                        expected_output: \"Intelligent ticket routing system with priority and category assignment\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"8\",\n                        description: \"Generate contextual customer responses\",\n                        expected_output: \"Personalized, helpful responses to customer inquiries\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"Support Team\",\n                    verified: true\n                },\n                requirements: [\n                    \"NLP capabilities\",\n                    \"Knowledge base access\",\n                    \"Ticket system integration\"\n                ],\n                outputs: [\n                    \"Automated ticket routing\",\n                    \"AI-generated responses\",\n                    \"Escalation alerts\",\n                    \"Performance metrics\"\n                ]\n            },\n            {\n                id: \"4\",\n                name: \"Sales Lead Generator\",\n                description: \"Automated lead generation and qualification system with personalized outreach sequences\",\n                category: \"sales\",\n                difficulty: \"intermediate\",\n                estimatedTime: \"2-3 hours\",\n                usageCount: 1543,\n                rating: 4.6,\n                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(),\n                tags: [\n                    \"lead-generation\",\n                    \"sales-automation\",\n                    \"outreach\",\n                    \"qualification\"\n                ],\n                agents: [\n                    {\n                        id: \"9\",\n                        role: \"Lead Researcher\",\n                        goal: \"Find and qualify potential leads based on specified criteria\",\n                        backstory: \"Sales development expert with experience in lead research and qualification across various industries.\",\n                        tools: [\n                            \"lead_research\",\n                            \"company_analysis\",\n                            \"contact_discovery\",\n                            \"qualification_scoring\"\n                        ]\n                    },\n                    {\n                        id: \"10\",\n                        role: \"Outreach Specialist\",\n                        goal: \"Create personalized outreach messages and follow-up sequences\",\n                        backstory: \"Sales communication expert specializing in cold outreach and relationship building.\",\n                        tools: [\n                            \"message_personalization\",\n                            \"sequence_creation\",\n                            \"follow_up_automation\",\n                            \"response_tracking\"\n                        ]\n                    }\n                ],\n                tasks: [\n                    {\n                        id: \"9\",\n                        description: \"Research and qualify potential leads\",\n                        expected_output: \"Qualified lead list with contact information and qualification scores\",\n                        status: \"pending\"\n                    },\n                    {\n                        id: \"10\",\n                        description: \"Create personalized outreach campaigns\",\n                        expected_output: \"Personalized email sequences with follow-up automation\",\n                        status: \"pending\"\n                    }\n                ],\n                author: {\n                    name: \"Sales Team\",\n                    verified: true\n                },\n                requirements: [\n                    \"Lead research tools\",\n                    \"Email automation\",\n                    \"CRM integration\"\n                ],\n                outputs: [\n                    \"Qualified lead lists\",\n                    \"Personalized outreach messages\",\n                    \"Follow-up sequences\",\n                    \"Response tracking\"\n                ]\n            }\n        ];\n        setCategories(mockCategories);\n        setTemplates(mockTemplates);\n    }\n    return {\n        templates,\n        categories,\n        isLoading,\n        error,\n        refetch: fetchTemplates\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/hooks/use-templates.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasSupabaseCredentials: () => (/* binding */ hasSupabaseCredentials),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Use fallback values for development when env vars are not set\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || \"https://placeholder.supabase.co\";\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || \"placeholder-key\";\n// Create a mock client if no real credentials are provided\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Helper to check if we have real Supabase credentials\nconst hasSupabaseCredentials = ()=>{\n    return process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY && process.env.NEXT_PUBLIC_SUPABASE_URL !== \"https://placeholder.supabase.co\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   formatTimeFromSeconds: () => (/* binding */ formatTimeFromSeconds),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatTimeFromSeconds(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n}\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + \"M\";\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + \"K\";\n    }\n    return num.toString();\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = seconds % 60;\n    if (hours > 0) {\n        return `${hours}h ${minutes}m ${remainingSeconds}s`;\n    } else if (minutes > 0) {\n        return `${minutes}m ${remainingSeconds}s`;\n    } else {\n        return `${remainingSeconds}s`;\n    }\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./store/workflow-store.ts":
/*!*********************************!*\
  !*** ./store/workflow-store.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWorkflowStore: () => (/* binding */ useWorkflowStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\nconst useWorkflowStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        selectedWorkflow: null,\n        isExecuting: false,\n        isExecutionPanelOpen: false,\n        executionStatus: \"idle\",\n        setSelectedWorkflow: (id)=>set({\n                selectedWorkflow: id\n            }),\n        toggleExecutionPanel: ()=>set((state)=>({\n                    isExecutionPanelOpen: !state.isExecutionPanelOpen\n                })),\n        startExecution: ()=>set({\n                isExecuting: true,\n                executionStatus: \"running\",\n                isExecutionPanelOpen: true\n            }),\n        pauseExecution: ()=>set({\n                executionStatus: \"paused\"\n            }),\n        stopExecution: ()=>set({\n                isExecuting: false,\n                executionStatus: \"idle\"\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./store/workflow-store.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"33e1a610ba87\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmV3Y3JhZnQtYWktcGxhdGZvcm0vLi9hcHAvZ2xvYmFscy5jc3M/NjgyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjMzZTFhNjEwYmE4N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"CrewCraft AI Platform - Multi-Agent AI Orchestration\",\n    description: \"Create, manage, and deploy AI agent crews with Cerebras ultra-fast inference. Build sophisticated multi-agent workflows for complex tasks.\",\n    keywords: \"AI, CrewAI, Cerebras, Multi-Agent, Artificial Intelligence, Automation, Workflows\",\n    authors: [\n        {\n            name: \"CrewCraft Team\"\n        }\n    ],\n    openGraph: {\n        title: \"CrewCraft AI Platform\",\n        description: \"Enterprise-grade multi-agent AI orchestration platform\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-slate-50 text-slate-800\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            className: \"bg-white shadow-lg border border-slate-200\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/templates/page.tsx":
/*!********************************!*\
  !*** ./app/templates/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/app/templates/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e1),
/* harmony export */   useAppContext: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#useAppContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/Projects/AICrewDecker/components/providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/framer-motion","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/@heroicons","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/tailwind-merge"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frajshah%2FDownloads%2FProjects%2FAICrewDecker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();