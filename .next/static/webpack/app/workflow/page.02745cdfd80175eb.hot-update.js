"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workflow/page",{

/***/ "(app-pages-browser)/./app/workflow/page.tsx":
/*!*******************************!*\
  !*** ./app/workflow/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WorkflowPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(app-pages-browser)/./components/layout/sidebar.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./components/layout/header.tsx\");\n/* harmony import */ var _components_workflow_workflow_canvas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/workflow/workflow-canvas */ \"(app-pages-browser)/./components/workflow/workflow-canvas.tsx\");\n/* harmony import */ var _components_execution_execution_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/execution/execution-panel */ \"(app-pages-browser)/./components/execution/execution-panel.tsx\");\n/* harmony import */ var _store_workflow_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/workflow-store */ \"(app-pages-browser)/./store/workflow-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction WorkflowPage() {\n    _s();\n    const { isExecutionPanelOpen } = (0,_store_workflow_store__WEBPACK_IMPORTED_MODULE_5__.useWorkflowStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workflow_workflow_canvas__WEBPACK_IMPORTED_MODULE_3__.WorkflowCanvas, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            isExecutionPanelOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    width: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    width: 400,\n                                    opacity: 1\n                                },\n                                exit: {\n                                    width: 0,\n                                    opacity: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"border-l border-slate-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_execution_execution_panel__WEBPACK_IMPORTED_MODULE_4__.ExecutionPanel, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Projects/AICrewDecker/app/workflow/page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowPage, \"eGkXED+i4i0mBJzjomDOHnNiArA=\", false, function() {\n    return [\n        _store_workflow_store__WEBPACK_IMPORTED_MODULE_5__.useWorkflowStore\n    ];\n});\n_c = WorkflowPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workflow/page.tsx\n"));

/***/ })

});